<?php
switch ($boton) {
    case $i18n[162]: // Cerrar
        bloque_inicio('nuevo_idtipocliente');
        {
            selector('idtipocliente', $i18n[61], '', '25', 'categorias_clientes', 'nombre', true, true, true);
        }
        bloque_fin();
        $script = '
        $("#cuerpo .idtipocliente").replaceWith($("#nuevo_idtipocliente").html());
        $("#cuerpo .idtipocliente option[value="+window.idtipocliente+"]").attr("selected", "selected");';
        modal_cerrar($script);
        break;
}

$resultado_sql = consulta_sql("SELECT idtipocliente, categorias_clientes.nombre, cuentacorriente, maxcuentacorriente, pagacosto, descuento, condicion, listas.idlista, listas.nombre AS lista
    FROM categorias_clientes
    LEFT JOIN listas ON categorias_clientes.idlista = listas.idlista
    ORDER BY categorias_clientes.nombre");

ventana_inicio($i18n[46], '100');
{
    contenido_inicio();
    {
        linea_inicio('titulo', 2);
        {
            celda('texto', $i18n[59], '20');
            celda('texto', $i18n[524], '22');
            celda('texto', $i18n[203], '6', false, false, false, $i18n[122]);
            celda('texto', $i18n[131], '15', false, false, false, $i18n[123]);
            celda('texto', $i18n[132], '6.5', false, false, false, $i18n[124]);
            celda('texto', $i18n[133], '15', false, false, false, $i18n[125]);
            celda('texto', $i18n[549], '', false, false, false);
        }
        linea_fin();

        bloque_inicio('listado');
        {
            while ($tipocliente = array_sql($resultado_sql)) {
                linea_inicio('fila', 2, '', 'id="linea_'.$tipocliente['idtipocliente'].'"');
                {
                    celda('texto', $tipocliente['nombre'], '20');
                    celda('texto', $i18n[$tipocliente['condicion']], '22');
                    marca('cuentacorriente', $tipocliente['cuentacorriente'], '7', 'disabled="disabled"');
                    celda('moneda', $tipocliente['maxcuentacorriente'], '15');
                    marca('pagacosto', $tipocliente['pagacosto'], '8', 'disabled="disabled"');
                    celda('descuento', $tipocliente['descuento'], '15');
                    celda('texto', $tipocliente['lista']);
                }
                linea_fin(array(
                    array('tipo' => 'ajax', 'url' => 'categorias_clientes.php?a=mod&id='.$tipocliente['idtipocliente'], 'a' => 'mod', 'title' => $i18n[62], 'opciones' => 'id="mod_'.$tipocliente['idtipocliente'].'"'),
                    array('tipo' => 'ajax', 'url' => 'categorias_clientes.php?a=baja&id='.$tipocliente['idtipocliente'], 'a' => 'baja', 'title' => $i18n[63])), 'id="botones_'.$tipocliente['idtipocliente'].'"');
                linea_inicio('fila', 2, '', 'id="entrada_'.$tipocliente['idtipocliente'].'" style="display: none;"');
                {
                    // No dejo modificar el nombre del tipo cliente Sin especificar
                    if ($tipocliente['idtipocliente'])
                        entrada('texto', 'nombre', '', $tipocliente['nombre'], '20', '60');
                    else
                        celda('texto', $tipocliente['nombre'], '20');

                    selector_array('condicion', '', $tipocliente['condicion'], '22',
                        array(
                        array('id' => 'contado', 'valor' => $i18n['contado']),
                        array('id' => 'cuentacorriente', 'valor' => $i18n['cuentacorriente']),
                        array('id' => 'debito', 'valor' => $i18n['debito']),
                        array('id' => 'credito', 'valor' => $i18n['credito']),
                        array('id' => 'cheque', 'valor' => $i18n['cheque']),
                        array('id' => 'ticket', 'valor' => $i18n['ticket']),
                        array('id' => 'otra', 'valor' => $i18n['otra'])
                    ));
                    marca('cuentacorriente', $tipocliente['cuentacorriente'], '7');
                    entrada('moneda', 'maxcuentacorriente', '', $tipocliente['maxcuentacorriente'], '15', '9');
                    marca('pagacosto', $tipocliente['pagacosto'], '8');
                    entrada('descuento', 'descuento', '', $tipocliente['descuento'], '15', '');
                    selector('idlista', '', $tipocliente['idlista'], '12', 'listas', 'idlista', false, false);
                }
                linea_fin(array(
                    array('tipo' => 'ajax', 'url' => 'categorias_clientes.php?a=mod&id='.$tipocliente['idtipocliente'], 'a' => 'ok', 'value' => $i18n[65]),
                    array('tipo' => 'ajax', 'url' => 'categorias_clientes.php?a=baja&id='.$tipocliente['idtipocliente'], 'a' => 'no', 'value' => $i18n[66])));
            }
        }
        bloque_fin();

        linea_inicio('fila', 2, '', 'id="linea_agregar"');
        {
            entrada('texto', 'nombre', '', '', '20', '60');
            selector_array('condicion', '', '', '22',
                array(
                array('id' => 'contado', 'valor' => $i18n['contado']),
                array('id' => 'cuentacorriente', 'valor' => $i18n['cuentacorriente']),
                array('id' => 'debito', 'valor' => $i18n['debito']),
                array('id' => 'credito', 'valor' => $i18n['credito']),
                array('id' => 'cheque', 'valor' => $i18n['cheque']),
                array('id' => 'ticket', 'valor' => $i18n['ticket']),
                array('id' => 'otra', 'valor' => $i18n['otra'])
            ));
            marca('cuentacorriente', '', '7');
            entrada('moneda', 'maxcuentacorriente', '', '', '15', '9');
            marca('pagacosto', '', '8');
            entrada('descuento', 'descuento', '', '', '15', '5');
            selector('idlista', '', $tipocliente['idlista'], '12', 'listas', 'idlista', false, false);
        }
        linea_fin(array(
            array(),
            array('tipo' => 'ajax', 'url' => 'categorias_clientes.php?a=alta', 'a' => 'alta', 'title' => $i18n[78])));
    }
    contenido_fin();

    botones(array(array('valor' => $i18n[162])));
}
ventana_fin();

?>
<script type="text/javascript">
    function categorias_clientes_alta_vaciar()
    {
        $("#linea_agregar option:selected").removeAttr("selected");
        $("#linea_agregar input").val("");
        $("#linea_agregar :checked").removeAttr("checked");
    }
</script>
