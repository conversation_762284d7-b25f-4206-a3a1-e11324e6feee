<?php

switch ($boton) {
    case $i18n_funciones[22]: // Aceptar
        $datos = recibir_matriz(array('idcaja_origen', 'idcaja_destino', 'idconcepto', 'monto', 'fecha'));
        if (!$datos['idcaja_origen'] || !$datos['idcaja_destino']) {
            script_flotante('alerta', $i18n[121], '7000');
        } elseif (!$datos['monto'] || $datos['monto'] <= 0) {
            script_flotante('alerta', $i18n[122], '7000');
        } else {
            $tipo_caja_origen = campo_sql(consulta_sql("SELECT tipo FROM categorias_cajas WHERE idcaja = '".$datos['idcaja_origen']."' LIMIT 1"));
            $tipo_caja_destino = campo_sql(consulta_sql("SELECT tipo FROM categorias_cajas WHERE idcaja = '".$datos['idcaja_destino']."' LIMIT 1"));

            if ($tipo_caja_origen == 'cheque' || $tipo_caja_destino == 'cheque') {
                $resultado_sql_movimientosxcajas = array_sql(consulta_sql("SELECT idrelacion, tiporelacion FROM movimientosxcajas WHERE idmovimientoxcaja = '".$id."'"));
                if ($resultado_sql_movimientosxcajas['tiporelacion'] == 'clientepago') {
                    $tabla_temp = 'ventaspagos';
                    $idtabla_temp = 'idventapago';
                } elseif ($resultado_sql_movimientosxcajas['tiporelacion'] == 'proveedorpago') {
                    $tabla_temp = 'compraspagos';
                    $idtabla_temp = 'idcomprapago';
                } else {
                    script_flotante('alerta', $i18n[123], '7000');
                    modal_cerrar();
                    break;
                }
                $resultado_sql_pagos = array_sql(consulta_sql("SELECT idrelacion, tiporelacion FROM ".$tabla_temp." WHERE ".$idtabla_temp." = '".$resultado_sql_movimientosxcajas['idrelacion']."'"));

                $numero = campo_sql(consulta_sql("SELECT numero FROM cheques WHERE idcheque = '".$resultado_sql_pagos['idrelacion']."' LIMIT 1"), 0);
                if ($tipo_caja_origen == 'cheque') {
                    $detallecajadesde = $i18n[109] . $numero . ' (Caja ' .campo_sql(consulta_sql("SELECT nombre FROM categorias_cajas WHERE idcaja = '".$datos['idcaja_origen']."' LIMIT 1"), 0) .')';
                    $detallecajahacia = $i18n[109] . $numero . ' (Caja ' .campo_sql(consulta_sql("SELECT nombre FROM categorias_cajas WHERE idcaja = '".$datos['idcaja_destino']."' LIMIT 1"), 0) .')';
                } else if ($tipo_caja_destino == 'cheque') {
                    $detallecajadesde = $i18n[108] . $numero . ' (Caja ' .campo_sql(consulta_sql("SELECT nombre FROM categorias_cajas WHERE idcaja = '".$datos['idcaja_origen']."' LIMIT 1"), 0) .')';
                    $detallecajahacia = $i18n[108] . $numero . ' (Caja ' .campo_sql(consulta_sql("SELECT nombre FROM categorias_cajas WHERE idcaja = '".$datos['idcaja_destino']."' LIMIT 1"), 0) .')';
                }
            } else {
                $detallecajadesde = $i18n[88] // Transferencia desde
                    .campo_sql(consulta_sql("SELECT nombre FROM categorias_cajas WHERE idcaja = '".$datos['idcaja_origen']."' LIMIT 1"), 0);
                $detallecajahacia = $i18n[89] // Transferencia hacia
                    .campo_sql(consulta_sql("SELECT nombre FROM categorias_cajas WHERE idcaja = '".$datos['idcaja_destino']."' LIMIT 1"), 0);
            }

            $idmoneda_caja_origen = idmoneda('cajas', $datos['idcaja_origen']);
            $idmoneda_caja_destino = idmoneda('cajas', $datos['idcaja_destino']);

            consulta_sql("INSERT INTO movimientosxcajas
                (idcaja, idusuario, idconcepto, fecha, total, detalle, tiporelacion, idrelacion) VALUES
                ('".$datos['idcaja_origen']."', '".$_SESSION['usuario_idusuario']."', '".$datos['idconcepto']."', '".fecha_sql($datos["fecha"])."', '-".$datos['monto']."', '".escape_sql($detallecajahacia)."', 'transferencia', '0')");
            $id_movimiento = id_sql();
            consulta_sql("INSERT INTO movimientosxcajas
                (idcaja, idusuario, idconcepto, fecha, total, detalle, tiporelacion, idrelacion) VALUES
                ('".$datos['idcaja_destino']."', '".$_SESSION['usuario_idusuario']."', '".$datos['idconcepto']."', '".fecha_sql($datos["fecha"])."', '".cotizacion($idmoneda_caja_destino, $idmoneda_caja_origen, $datos['monto'])."', '".escape_sql($detallecajadesde)."', 'transferencia', '".$id_movimiento."')");
            $id_movimiento2 = id_sql();
            consulta_sql("UPDATE movimientosxcajas SET idrelacion = ".$id_movimiento2." WHERE idmovimientoxcaja = ".$id_movimiento);

            $_SESSION['control_ultimoconceptotransferencia'] = $datos['idconcepto'];
            consulta_sql("UPDATE controles SET
                    ultimoconceptotransferencia = '".$_SESSION['control_ultimoconceptotransferencia']."'
                WHERE idusuario = '".$_SESSION['usuario_idusuario']."'
                LIMIT 1");

            if ($resultado_sql_pagos['tiporelacion'] == 'cheque') {
                consulta_sql("UPDATE cheques SET estado = 1 WHERE idcheque = '".$resultado_sql_pagos['idrelacion']."'");
                consulta_sql("UPDATE movimientosxcajas SET fechaconciliacion = '".date("Y-m-d H:i:s")."', idconciliacion = '".$resultado_sql_movimientosxcajas['idrelacion']."' WHERE idmovimientoxcaja = '".$id."'");
                consulta_sql("UPDATE movimientosxcajas SET conciliacion = 1, fechaconciliacion = '".date("Y-m-d H:i:s")."', idconciliacion = '".$resultado_sql_movimientosxcajas['idrelacion']."' WHERE idmovimientoxcaja = '".$id_movimiento."'");
                consulta_sql("UPDATE movimientosxcajas SET conciliacion = 1, fechaconciliacion = '".date("Y-m-d H:i:s")."', idconciliacion = '".$resultado_sql_movimientosxcajas['idrelacion']."' WHERE idmovimientoxcaja = '".$id_movimiento2."'");
            }

            actualizar_saldo('cajas', $datos['idcaja_origen'], $datos['monto'] * -1);
            actualizar_saldo('cajas', $datos['idcaja_destino'], cotizacion($idmoneda_caja_destino, $idmoneda_caja_origen, $datos['monto']));

            modal_cerrar('actualizar_marco_cajas_mod()');
        }
        break;

    case $i18n_funciones[23]: // Cancelar
        modal_cerrar();
        break;

    default:
        $idcaja = recibir_variable('idcaja');
        $ingreso = recibir_variable('ingreso');
        $egreso = recibir_variable('egreso');
        if($idcaja) {
            $id = $idcaja;
        } else {
            $datos = array(
                'idcaja_origen' => $id,
                );
        }
        break;
}

$ultimo_concepto = campo_sql(consulta_sql(
    "SELECT ultimoconceptotransferencia FROM controles
    WHERE idusuario='".$_SESSION['usuario_idusuario']."' LIMIT 1"), 0);

$resultado_sql = consulta_sql(
    "SELECT c.nombre, c.idtipocaja, c.idcaja, c.tipo,
        cajas.saldoapertura, cajas.fechacierre,
        saldos.saldo, monedas.simbolo
    FROM categorias_cajas AS c
        INNER JOIN cajas ON c.idcaja = cajas.idcaja
        LEFT JOIN saldos ON saldos.tiporelacion = 'cajas' AND saldos.idrelacion = cajas.idcaja
        LEFT JOIN monedas ON cajas.idmoneda = monedas.idmoneda
    WHERE estado = '1'
    AND cajas.fechacierre = '0000-00-00 00:00:00'"
    .($_SESSION['perfil_idperfil'] == '1'
        ? ""
        : " AND (c.compartida='1' OR EXISTS (
            SELECT 1 FROM perfilesxcajas
            WHERE perfilesxcajas.idtipocaja = c.idtipocaja
            AND idperfil='".$_SESSION['perfil_idperfil']."'
            AND alta='1'))"
    ));

/*$cajas = array(array(id => 0, 'valor' => $i18n[77]));
while ($caja = array_sql($resultado_sql)) {
    $cajas[] = array('id' => $caja['idcaja'], 'valor' => $caja['nombre']." ($ ".redondeo($caja["saldo"] + $caja["saldoapertura"]).")");
}
*/

$formasdepago_disponibles = array();
$cajas = array(array("id" => 0, 'valor' => $i18n[77], 'datasets' => array('data-tipoformapago' => 'sin_especificar')));
while ($array_tipo_caja = array_sql($resultado_sql)) {
    $cajas[] = array(
        'id' => $array_tipo_caja['idcaja'],
        'valor' => $array_tipo_caja['nombre']." (".$array_tipo_caja["simbolo"].' '.$array_tipo_caja["saldo"].")",
        'datasets' => array(
            'data-tipoformapago' => $array_tipo_caja['tipo'],
            )
    );
    if (!in_array($array_tipo_caja['tipo'], $formasdepago_disponibles))
        $formasdepago_disponibles[] = "'".$array_tipo_caja['tipo']."'";
}

//esto es para eliminar de la selección de cajas destino "retención" y "cheques" si estamos en banco o efectivo
$tipo_caja_origen = campo_sql(consulta_sql("SELECT tipo FROM categorias_cajas WHERE idcaja = '".$id."'"));
if ($egreso) {
    $tipo_caja_destino = campo_sql(consulta_sql("SELECT tipo FROM categorias_cajas WHERE idcaja = '".$id."'"));
    $tipo_caja_origen = '';
}
if($tipo_caja_origen == 'efectivo' || $tipo_caja_origen == 'banco') {
    ?>
        <script type="text/javascript" charset="utf-8">
            $(function() {
                $("select[name='idcaja_origen'] option[data-tipoformapago='cheque']").remove();
                $("select[name='idcaja_origen'] option[data-tipoformapago='retencion']").remove();
                $("select[name='idcaja_destino'] option[data-tipoformapago='cheque']").remove();
                $("select[name='idcaja_destino'] option[data-tipoformapago='retencion']").remove();
            });
        </script>
    <?php
}
//----------------------------------------------------------------------------------------------------------

ventana_inicio(($tipo_caja_origen == 'cheque' || $tipo_caja_destino == 'cheque' ? $i18n[105] : $i18n[76]), '66');
{
    /*genera todo el js para validar el form tomando las reglas definidas arriba en el array campos_reglas*/
    //generar_validador_js($i18n[76], $campos_reglas, $i18n_funciones[23], "#marco_flotante", 'cajas_transferencia');

    contenido_inicio();
    {
        if($tipo_caja_origen == 'cheque' || $tipo_caja_destino == 'cheque') {
            texto('italica', '', ($ingreso ? $i18n[106] : $i18n[107]), 'auto', false, 'info', false);
        }
        if($tipo_caja_origen != 'cheque') {
            selector_array('idcaja_origen', ($tipo_caja_destino == 'cheque' ? $i18n[111] : $i18n[83]), $datos['idcaja_origen'],
            '50', $cajas, $i18n[81], 'onchange="transferir_diferentes_cajas('."'origen', ".$id.')"');
        } else {
            entrada('hidden', 'idcaja_origen', '', $id);
        }
        if($tipo_caja_destino != 'cheque') {
            selector_array('idcaja_destino', ($tipo_caja_origen == 'cheque' ? $i18n[111] : $i18n[82]), $datos['idcaja_destino'],
            '50', $cajas, $i18n[81], 'onchange="transferir_diferentes_cajas('."'destino', ".$id.')"');
        } else {
            entrada('hidden', 'idcaja_destino', '', $id);
        }
        entrada('fechayhora', 'fecha', $i18n[10], 'ahora', '30','60', false);
        selector_familiar('idconcepto', $i18n[11], $ultimo_concepto, '50', 'categorias_conceptos', false, true, true);
        entrada('moneda', 'monto', $i18n[84], $datos['monto'], '20');
    }
    contenido_fin();

    botones(array(array('valor' => $i18n_funciones[22]), array('valor' => $i18n_funciones[23])));
}
ventana_fin();
?>
