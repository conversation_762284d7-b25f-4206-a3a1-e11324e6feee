function recalculando(datos)
{
    if(!datos.totaltributos) datos.totaltributos = '0,00';
    if(!datos.descuentoenpesos) datos.descuentoenpesos = '0,00';
    $("#subtotal").html(datos.simbolo + ' ' + datos.subtotal);
    $("#descuentoenpesos").html(datos.simbolo + ' ' + datos.descuentoenpesos);
    $("#descuentoenpesos").parent().find('span.campo_nombre').html('Descuento (' + datos.descuento + ' %): ');
    $("#totalnetos").html(datos.simbolo + ' ' + datos.totalnetos);
    $("#totalivas").html(datos.simbolo + ' ' + datos.totalivas);
    $("#totaltributos").html(datos.simbolo + ' ' + datos.totaltributos);
    $("#totales #total").html(datos.simbolo + ' ' + datos.total);
}

function trigger_ok(e) {
    if (e.keyCode == 13) {
        e.preventDefault();
        var entrada = $(":focus");
        var name = entrada.attr("name");
        if (name == "cantidad" || name == "precio" || name == "preciofinal" || name == "descuento") {
            entrada.parent().parent().parent().find('.ok').trigger("click");
        }
    }
}

function revisarAgregarPago()
{
    let condicionventa = $("select[name='condicionventa'] option:selected").val();
    if ((condicionventa != 'cuentacorriente' && condicionventa != 'cheque')
        && ($("input[name='muevesaldo']").is(":checked")
            || $("input[type='hidden'][name='muevesaldo']").val() == '1')) {

        $("#altapago").fadeIn();

    } else {
        $("#altapago").fadeOut();
        $("#detallepago").fadeOut();
    }

    if ($("#agregarpago").is(":checked")) {
        $("#detallepago").fadeIn();
    } else {
        $("#detallepago").fadeOut();
    }

}

function ventas_dni(tipodoc, dni, razonsocial, idlocalidad, localidad)
{
    $("#tipodoc .campo_texto").html(tipodoc);
    $("#ventas_dni .campo_texto").html(dni);
    $("#ventas_razonsocial .campo_texto").html(razonsocial);
    $("#ventas_localidad .campo_texto").html(localidad);
    $("#idlocalidad").val(idlocalidad);
}

function compras_cuit(tipoiva, modulo, id, cuit, razonsocial, idtipoiva, idlocalidad, localidad)
{
    $("#compras_tipoiva .campo_texto").html(tipoiva);
    $("#compras_cuit .campo_texto").html(cuit);
    $("#compras_razonsocial .campo_texto").html(razonsocial);
    $("#compras_localidad .campo_texto").html(localidad);
    $("#idlocalidad").val(idlocalidad);
    if(idtipoiva == 1)
        var letra = 'A';
    else
        var letra = 'C';

    recargar_formulario(false, modulo, 'Factura', id, letra);
}

function recargar_formulario(mensaje = false, modulo, tipocompra = false, id, letra = false){
    if (!mensaje || confirma(mensaje)){
        $.ajax(
        {
            url: "cargadores/ajax.php",
            type: "post",
            data: (
            {
                t: window.t,
                modulo : modulo,
                id: id,
                ventana : "recargar_formulario",
                letra: letra,
                tipocompra: tipocompra
            }),
            success: function(data) {
                desbloquear();
                window.location.href = modulo+'.php?a=mod&id='+id;
            }
        });
    }
}

function guardar_sucursal(tiposucursal, modulo, id, idtiposucursal) {
    if (confirma('Para aplicar esta modificación debemos recargar la página. Algunos cambios como fechas y datos adicionales recién modificados pueden perderse y deben volver a cargarse. ¿Desea continuar?')) {
        $.ajax(
        {
            url: "cargadores/ajax.php",
            type: "post",
            data: (
            {
                t: window.t,
                modulo : modulo,
                id: id,
                ventana : "guardar_sucursal",
                idtiposucursal: idtiposucursal,
                tiposucursal: tiposucursal
            }),
            success: function(data) {
                $("#"+tiposucursal+"_origen").val(idtiposucursal);
                desbloquear();
                window.confirmarsalida = false;
                window.location.replace(window.location.href); // Hay que recargar sí o sí, sino no contempla los cambios iddeposito y idlista de fullsearch
            }
        });
    } else {
        let idorigen = $("#"+tiposucursal+"_origen").val();
        $("select[name='"+tiposucursal+"']").val(idorigen);
        desbloquear();
    }
}

function guardar_moneda(modulo, idoperacion, idmoneda) {
    if (confirma('Para aplicar esta modificación debemos recargar la página. Algunos cambios como fechas y datos adicionales recién modificados pueden perderse y deben volver a cargarse. ¿Desea continuar?')) {
        $.ajax(
        {
            url: "cargadores/ajax.php",
            type: "post",
            data: (
            {
                t: window.t,
                modulo: modulo,
                id: idoperacion,
                ventana : "guardar_moneda",
                idmoneda: idmoneda,
            }),
            success: function(data) {
                desbloquear();
                window.confirmarsalida = false;
                window.location.replace(window.location.href); // Hay que recargar sí o sí, sino no contempla los cambios de moneda
            }
        });
    } else {
        let idorigen = $("#idmoneda_origen").val();
        $("select[name='idmoneda']").val(idorigen);
        desbloquear();
    }
}

function seleccionar_tipo_caja()
{
    var tipoformapago = $("select[name='idformapago'] option:selected").attr('data-tipoformapago');
    $("select[name='idcaja']").prop('disabled', false);
    var tipoformapago = $("select[name='idformapago'] option:selected").attr('data-tipoformapago');
    var idformapago = $("select[name='idformapago'] option:selected").val();

    if (tipoformapago == 'cheque'){
        $("select[name='idrelacion'] option:selected").removeAttr("selected");
    }
    //idformapago_5 = cheque
    //idformapago_10 = mercadopago
    //idformapago_13 = retenciones
    $("#contenido_idformapago_5, #contenido_idformapago_10, #contenido_idformapago_13").hide();
    $("#contenido_idformapago_" + idformapago).show();

    $("select[name='idcaja'] option:selected").removeAttr("selected");
    $("select[name='idcaja'] option").hide();
    $("select[name='idcaja'] option[data-tipoformapago='"+tipoformapago+"']").show();
    $("select[name='idcaja'] option[data-tipoformapago='"+tipoformapago+"']").attr("selected", "selected");
    $("input[name='total']").prop('disabled', false);
};

// TODO: Pasar a las validaciones del framework
function validacion_pago()
{
    if ($("#total").val() <= 0) {
        alerta_selector('alerta', 'El total es obligatorio', "input[name='total']");
        return false;
    } else {
        var tipoformapago = $("select[name='idformapago'] option:selected").attr('data-tipoformapago');
        if (tipoformapago == 'cheque'){
            if ($("#en_cartera").css('display') == "block" && !idcheque) {
                    alerta_selector('alerta', 'Seleccionar Cheque', "select[name='idcheque']");
                    return false;
            } else if (typeof idcheque === 'undefined'){

                if (!$("#fechacobro").val()){
                    alerta_selector('alerta', 'La fecha es obligatoria', "input[name='fechacobro']");
                    return false;

                }
                if (!$("#titular").val()){
                    alerta_selector('alerta', 'El titular es obligatorio', "input[name='titular']");
                    return false;
                }
                if (!$("#numero").val()){
                    alerta_selector('alerta', 'El número de cheque es obligatorio', "input[name='numero']");
                    return false;
                }
            }
        }
        /*if (tipoformapago == 'retencion'){
            if (!$("#tributo").val()){
                alerta_selector('alerta', 'El nombre de retención es obligatorio', "input[name='tributo']");
                return false;
            }
        }
        */
        $("select[name='idcaja']").prop('disabled', false);
        $('input[name="total"]').prop('disabled', false);
        return true;
    }
};

function validacion_ventaspagos_altamod(boton)
{
    var formapago = $("select[name='idformapago'] option:selected");
    return boton == 'Cancelar'
        ? true
        : validacion_pago();
}

function validacion_compraspagos_altamod(boton)
{
    var formapago = $("select[name='idformapago'] option:selected");
    return boton == 'Cancelar'
        ? true
        : validacion_pago();
}

function seleccionar_cheque_encartera()
{
    var totalcheque = $("select[name='idrelacion'] option:selected").attr('data-totalcheque');
    var idcaja = $("select[name='idrelacion'] option:selected").attr('data-idcaja');
    $("input[value='Agregar']").prop("disabled", false);

    $('input[name="total"]').val(totalcheque);
    if(totalcheque){
        $('input[name="total"]').prop('disabled', true);
    } else {
        $('input[name="total"]').prop('disabled', false);
    }

    if(typeof idcaja === 'undefined'){
        $('select[name="idcaja"]').prop('disabled', false);
        $("select[name='idcaja']").prop('disabled', true);
    } else if(!idcaja) { //si el cheque viene sin idcaja, mando alert
        $("select[name='idcaja']").prop('disabled', true);
        $("input[value='Agregar']").prop("disabled", true);
        alerta('No hay cajas abiertas en la cual agregar el movimiento. Re-abra o abra una nueva instancia de esa caja para poder agregar esta orden de pago');
    } else {
        $("select[name='idcaja']").val(idcaja);
        $("select[name='idcaja']").prop('disabled', true);
    }
}

function recargar_ventana_mercado(tipo_ventana)
{
    var idtienda = $("select[name='idtienda'] option:selected").val();
    window.location.href = 'ventas.php?a='+tipo_ventana+'&idtienda='+idtienda;
}

$(function() {

    $("a[href='vermas']").live(detectMobile() ? 'touchstart' : 'click', function(e) {
        e.preventDefault();
        $(this).parent().parent().find(".oculto").slideDown();
        $(this).attr("href","vermenos");
        $(this).html('<img title="" src="estilos/estilo_1/images/vermenos.png">');
        $(this).parent().parent().find(".oculto textarea").redactor();
    });
    $("a[href='vermenos']").live("click", function(e) {
        e.preventDefault();
        $(this).parent().parent().find(".oculto").slideUp();
        $(this).attr("href","vermas");
        $(this).html('<img title="" src="estilos/estilo_1/images/vermas.png">');
    });

    $("input[name='vencimiento1']").change(function() {
        if ($("input[name='vencimiento1']").val()) {
            $("#div_vencimiento2").fadeIn(400);
        } else {
            $("#div_vencimiento2").fadeOut(400);
        }
    });

    $("select[name='concepto']").change(function() {
        if ($("select[name='concepto'] option:selected").val() > 1) {
            $("#periodo_facturado").fadeIn(400);
        } else {
            $("#periodo_facturado").fadeOut(400);
        }
    });

    $("#linea_agregar #descuento").live("change", function() {
        if (parseFloat($("#linea_agregar #descuento").val()) > 100)
            $("#linea_agregar #descuento").val(100);
    });

    $("a").click(function(e) {
        $('input[name="total"]').prop('disabled', false);
        $('select[name="idcaja"]').prop('disabled', false);
        var tipocheque = $(e.target).text();
        if(tipocheque != 'Propio'){
            //$('input[name="total"]').val('');
            $("select[name='idrelacion']").val(0);
        }
    });

    if ($('#tipocheque').val() == 'tercero'){
        $('li:has(a[href="#en_cartera"])').click();
//        idcheque = <?php echo json_encode($cheques['idcheque']);?>;
        $("select[name='idcheque']").val(idcheque).change;
        $('input[name="total"]').prop('disabled', true);
        $('#fechacobro').val('');
        $('#titular').val('');
        $('#numero').val('');
    }

    $("#agregarpago").click(function() {
        revisarAgregarPago();
    });
    $("select[name='condicionventa']").change(function() {
        revisarAgregarPago();
    });
    $("input[name='muevesaldo']").click(function() {
        revisarAgregarPago();
    });
    revisarAgregarPago();
});

function reimprimir_row_unificar(datos) {
    const $lineaContenido = $('#idx[value="'+datos.idx+'"]').closest('.linea_contenido');
    const $inputCantidad = $lineaContenido.find('input#cantidad');
    const precio = parseFloat($lineaContenido.find('input#precio').val()) || 0;
    const preciofinal = parseFloat($lineaContenido.find('input#preciofinal').val()) || 0;
    const descuento = parseInt($lineaContenido.find('input#descuento').val()) || 0;
    const nuevaCantidad = parseFloat(datos.cantidad) + parseFloat(datos.cantidad_original);
    const nuevototal = (precio + preciofinal) * nuevaCantidad;
    const nuevototalDescuento = nuevototal - (nuevototal * (descuento / 100));
    $inputCantidad.val(nuevaCantidad.toFixed(2));
    $lineaContenido.find('.celda').eq(1).text(nuevaCantidad.toFixed(2));
    $lineaContenido.find('.celda').eq(9).text(datos.simbolo + ' '+ nuevototalDescuento.toFixed(2))
    $lineaContenido.find('.celda_auto ').text(datos.simbolo + ' '+ nuevototalDescuento.toFixed(2));
}