<?php

if ($boton) {
    switch ($boton) {
        case $i18n[112]: // Ordernar fecha por cobro / pago
            actualizar_controles(array('cheques_ordenar' => 'fechacobro DESC'));
            break;
        case $i18n[113]: // Ordenar por fecha de movimiento
            actualizar_controles(array('cheques_ordenar' => 'fecha DESC'));
            break;
    }
}

controlar_saldo('cajas', $id);

$caja = array_sql(consulta_sql(
    "SELECT cajas.*,
        categorias_cajas.*,
        saldos.saldo,
        (SELECT SUM(total) FROM movimientosxcajas WHERE idcaja = $id AND total > 0) AS totalingreso,
        (SELECT SUM(total) FROM movimientosxcajas WHERE idcaja = $id AND total < 0) AS totalegreso,
        (SELECT SUM(total) FROM movimientosxcajas WHERE idcaja = $id AND conciliacion = '1') AS totalconciliado,
        (SELECT SUM(total) FROM movimientosxcajas WHERE idcaja = $id AND conciliacion = '0') AS totalsinconciliar,
        monedas.nombre AS moneda, monedas.simbolo
    FROM cajas
        INNER JOIN categorias_cajas ON cajas.idtipocaja = categorias_cajas.idtipocaja
        LEFT JOIN movimientosxcajas ON cajas.idcaja = movimientosxcajas.idcaja
        LEFT JOIN saldos ON saldos.tiporelacion = 'cajas' AND saldos.idrelacion = cajas.idcaja
        LEFT JOIN monedas ON cajas.idmoneda = monedas.idmoneda
    WHERE cajas.idcaja = '$id'
    LIMIT 1")
);

$caja = controlar_moneda('cajas', $caja);

mensajes_efimeros();

ventana_inicio($i18n[66] . $caja['nombre'], '100', array(
    array('tipo' => 'imagen', 'url' => 'cajas.php?a=mod&id=' . $id, 'a' => 'mod', 'title' => $i18n[34]),
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[35])));
{
    // COPIADO A: cajas_mod_vista
    // Situación de la caja
    contenido_inicio($i18n[110], 50);
    {
        celda('imagen', $i18n['caja_'.$caja['tipo']], 'imagen', 'caja_'.$caja['tipo']);
        texto('titulo', '',  $i18n['caja_'.$caja['tipo']], '25');
        if ($_SESSION['modulo_multimoneda']) {
            texto('titulo', $i18n[126], $caja['moneda'].' ('.$caja['simbolo'].')');
        }
    }
    contenido_fin();
    contenido_inicio($i18n[3], 50);
    {
        // La caja está abierta si no tiene fecha de cierre
        $caja_abierta = $caja['fechacierre'] == '0000-00-00 00:00:00'
            ? true : false;
        $saldo = $caja_abierta
            ? $caja['saldo']
            : $caja['saldocierre'];

        // Fecha apertura
        texto('fechayhora', $i18n[29], $caja['fechaapertura']);
        // Estado abierta o fecha de cierre
        if ($caja_abierta)
            texto('texto', $i18n[22], $i18n[23]);
        else
            texto('fechayhora', $i18n[32], $caja['fechacierre']);
        // Saldo inicial
        texto('moneda', $i18n[19], $caja['saldoapertura'], false, false, false, false, false, $caja['simbolo']);
        // Total ingreso
        texto('moneda', $i18n[101], ($caja['totalingreso'] ? $caja['totalingreso'] : 0), false, false, false, false, false, $caja['simbolo']);
        // Total egreso
        texto('moneda', $i18n[102], ($caja['totalegreso'] ? $caja['totalegreso'] : 0), false, false, false, false, false, $caja['simbolo']);
        // Saldo conciliado salvo en caja efectivo
        if ($caja['tipo'] != 'efectivo' && $caja['tipo'] != 'cheque')
            texto('moneda', $i18n[71], $caja['saldoapertura'] + $caja['totalconciliado'], false, false, false, false, 'id="totalconciliado"', $caja['simbolo']);
        // Saldo conciliado salvo en caja efectivo
        if ($caja['tipo'] != 'efectivo')
            texto('moneda', $i18n[72], ($caja['totalsinconciliar'] ? $caja['totalsinconciliar'] : 0), false, false, false, false, false, $caja['simbolo']);
        // Saldo actual o saldo de cierre
        if ($caja_abierta)
            texto('moneda', $i18n[6], $caja['saldo'], false, false, false, false, false, $caja['simbolo']);
        else
            texto('moneda', $i18n[45], $caja['saldocierre'], false, false, false, false, false, $caja['simbolo']);
    }
    contenido_fin();
    // COPIADO A: cajas_mod_vista

    // Movimientos de la caja
    contenido_inicio($i18n[44]);
    {
        if ($caja['tipo'] == 'cheque')
            texto('italica', '', $i18n[104], 'auto', false, 'alerta', false);

        linea_inicio('titulo', ($caja['tipo'] == 'cheque' ? 1 : 0));
        {
            if ($caja['tipo'] != 'efectivo')
                celda('texto', '', 'imagen', false, false, false, $i18n[73]);
            celda('texto', ($caja['tipo'] == 'cheque' ? ($_SESSION['control_cheques_ordenar'] == 'fechacobro DESC' ? $i18n[103] : $i18n[114]) : $i18n[10]), '12');
            celda('texto', $i18n[11], '20');
            celda('texto', $i18n[12], '33');
            celda('texto', $i18n[13], '10');
            celda('texto', $i18n[14], '10');
            celda('texto', $i18n[15], '8');
        }
        linea_fin(($caja['tipo'] == 'cheque')
            ? array(
                array('tipo' => 'desplegable', 'a' => 'filtrar', 'title' => $i18n_funciones[70], 'url' => 'cajas_ver', 'desplegable' => array(
                    array('a' => ($_SESSION['control_cheques_ordenar'] != 'fechacobro DESC' ? $i18n[112] : $i18n[113])),
                )))
            : array()
        );

        if ($caja['tipo'] == 'cheque' && $_SESSION['control_cheques_ordenar'] == 'fechacobro DESC')
            $orden = 'IF(fechacobro IS NULL, fechaconciliacion , fechacobro) DESC';
        else
            $orden = 'fecha DESC';

        $orden .= ', idmovimientoxcaja ASC';

        $resultado_sql = consulta_sql("SELECT movimientosxcajas.*, cheques.fechacobro, compraspagos.idnumerocomprapago AS idnumero,
                    categorias_conceptos.padres, categorias_conceptos.idconceptopadre, categorias_conceptos.nombre AS concepto
                FROM movimientosxcajas
                    LEFT JOIN categorias_conceptos ON movimientosxcajas.idconcepto = categorias_conceptos.idconcepto
                    LEFT JOIN compraspagos ON movimientosxcajas.idrelacion = compraspagos.idcomprapago
                    LEFT JOIN cheques ON compraspagos.idrelacion = cheques.idcheque AND compraspagos.tiporelacion = 'cheque'
                WHERE movimientosxcajas.idcaja = '$id'
                    AND movimientosxcajas.tiporelacion = 'proveedorpago'

                UNION

                SELECT movimientosxcajas.*, cheques.fechacobro, ventaspagos.idnumeroventapago AS idnumero,
                    categorias_conceptos.padres, categorias_conceptos.idconceptopadre, categorias_conceptos.nombre AS concepto
                FROM movimientosxcajas
                    LEFT JOIN categorias_conceptos ON movimientosxcajas.idconcepto = categorias_conceptos.idconcepto
                    LEFT JOIN ventaspagos ON movimientosxcajas.idrelacion = ventaspagos.idventapago
                    LEFT JOIN cheques ON ventaspagos.idrelacion = cheques.idcheque AND ventaspagos.tiporelacion = 'cheque'
                WHERE movimientosxcajas.idcaja = '$id'
                    AND movimientosxcajas.tiporelacion = 'clientepago'

                UNION

                SELECT movimientosxcajas.*, COALESCE(null) AS fechacobro, COALESCE(0) AS idnumero, categorias_conceptos.padres,
                    categorias_conceptos.idconceptopadre, categorias_conceptos.nombre AS concepto
                FROM movimientosxcajas
                    LEFT JOIN categorias_conceptos ON movimientosxcajas.idconcepto = categorias_conceptos.idconcepto
                    LEFT JOIN cajas ON movimientosxcajas.idcaja = cajas.idcaja
                WHERE movimientosxcajas.idcaja = '$id'
                    AND movimientosxcajas.tiporelacion != 'clientepago' AND movimientosxcajas.tiporelacion != 'proveedorpago'

                ORDER BY ".$orden."
                LIMIT ".$_SESSION['control_agrandar']);

        $i = 0;
        while ($ordenar[$i] = array_sql($resultado_sql)) {
            $saldo -= $ordenar[$i]['total'];
            $i++;
        }
        if ($i == $_SESSION['control_agrandar']) {
            agrandar(array(
                array('valor' => $i18n[63], 'url' => 'cajas_agrandar', 'cantidad' => $_SESSION['control_agrandar'], 'tipo' => $caja['tipo'], 'id_boton' => $caja['tipo'].'_'.$saldo, 'arriba' => true),
                array('valor' => $i18n[64], 'url' => 'cajas_agrandar', 'cantidad' => $_SESSION['control_agrandar'], 'opciones' => 'todos', 'id_boton' => $caja['tipo'].'_'.$saldo, 'arriba' => true)
                ));
        }
        while ($i > 0) {
            $i--;
            $movimiento = $ordenar[$i];
            $movimiento['tipo'] = $caja['tipo'];
            linea_inicio('fila', ($caja['tipo'] == 'cheque' ? 1 : 0), '', 'id="linea_' . $movimiento['idmovimientoxcaja'] . '"');
            {
                // COPIADO A CAJAS_AGRANDAR
                if ($movimiento['total'] >= 0) {
                    $saldo += $movimiento['total'];
                    $movimiento['ingreso'] = $movimiento['total'];
                    $movimiento['egreso'] = '';
                } else {
                    $saldo += $movimiento['total'];
                    $movimiento['egreso'] = -$movimiento['total'];
                    $movimiento['ingreso'] = '';
                }

                switch ($movimiento['tiporelacion']) {
                    case 'clientepago':
                        $link = 'ventas.php?a=verpago&id=' . $movimiento['idrelacion'];
                        if (strpos($movimiento['detalle'], $i18n[124]) !== false) {
                            $movimiento['detalle'] = $i18n[124] . 'RP'.completar_numero($movimiento['idnumero'], 8);
                        }
                        $numero_extra = campo_sql(consulta_sql(
                            "SELECT numero
                            FROM ventasxclientes
                            WHERE idtipoventa = 0
                                AND ventasxclientes.id = " . $movimiento['idrelacion'] . "
                            LIMIT 1"    ), 0);
                        $nombre_extra = campo_sql(consulta_sql(
                            "SELECT nombre
                            FROM clientes
                            WHERE clientes.idcliente =
                                (SELECT idcliente FROM ventaspagos WHERE ventaspagos.idventapago = " . $movimiento['idrelacion'] . " LIMIT 1)
                            LIMIT 1"    ), 0);
                        $detalle_extra = " (" . $nombre_extra . " | " . ($numero_extra ? $numero_extra : $i18n[75]) . ")";
                        break;

                    case 'proveedorpago':
                        $link = 'compras.php?a=verpago&id=' . $movimiento['idrelacion'];
                        if (strpos($movimiento['detalle'], $i18n[125]) !== false) {
                            $movimiento['detalle'] = $i18n[125] . 'OP'.completar_numero($movimiento['idnumero'], 8);
                        }
                        $numero_extra = campo_sql(consulta_sql(
                            "SELECT numero
                            FROM comprasxproveedores
                            WHERE idtipocompra = 0
                                AND comprasxproveedores.id = " . $movimiento['idrelacion'] . "
                            LIMIT 1"    ), 0);
                        $nombre_extra = campo_sql(consulta_sql(
                            "SELECT nombre
                            FROM proveedores
                            WHERE proveedores.idproveedor =
                                (SELECT idproveedor FROM compraspagos WHERE compraspagos.idcomprapago = " . $movimiento['idrelacion'] . " LIMIT 1)
                            LIMIT 1"    ), 0);
                        $detalle_extra = " (" . $nombre_extra . " | " . ($numero_extra ? $numero_extra : $i18n[75]) . ")";
                        break;

                    default:
                        $link = false;
                        $detalle_extra = '';
                        break;
                }

                if ($movimiento['padres']) {
                    $movimiento['concepto'] = nombre_con_padres('categorias_conceptos', $movimiento['idconcepto']);
                }

                if ($movimiento['tipo'] == 'cheque') {
                    if ($movimiento['tiporelacion'] == 'transferencia') {
                        celda('largo', '', '3');    //Nada!
                    } else {
                        marca('conciliacion_actual' . $movimiento['idmovimientoxcaja'], $movimiento['conciliacion'], 'imagen', 'disabled="disabled"');
                    }
                    if($movimiento['tiporelacion'] != 'transferencia' && $_SESSION['control_cheques_ordenar'] == 'fechacobro DESC')
                        $fecha = $movimiento['fechacobro'];
                    elseif($movimiento['tiporelacion'] == 'transferencia' && $_SESSION['control_cheques_ordenar'] == 'fechacobro DESC')
                        $fecha = $movimiento['fechaconciliacion'];
                    else
                        $fecha = $movimiento['fecha'];
                    celda('fecha', $fecha, '12');
                } else if ($movimiento['tipo'] == 'banco' || $movimiento['tipo'] == 'retencion'){
                    marca('conciliacion_'.$movimiento['idmovimientoxcaja'], $movimiento['conciliacion'], 'imagen', 'disabled="disabled"');
                    celda('fechayhora', $movimiento['fecha'], '12');
                } else {
                    celda('fechayhora', $movimiento['fecha'], '12');
                }
                celda('largo', $movimiento['concepto'], '20');
                if ($detalle_extra) {
                    celda('largo', $movimiento['detalle'], '19', false, $link);
                    celda('largo', $detalle_extra, '14');
                } else {
                    celda('largo', $movimiento['detalle'] . $moviento['detalle'], '33', false, $link);
                }
                celda('moneda', $movimiento['ingreso'], '10', false, false, false, false, $caja['simbolo']);
                celda('moneda', $movimiento['egreso'], '10', false, false, false, false, $caja['simbolo']);
                celda('moneda', $saldo, false, false, false, false, false, $caja['simbolo']);
            }
            // COPIADO A CAJAS_AGRANDAR
            linea_fin();
        }
    }
    contenido_fin();

    if ($caja['observacion']) {
        contenido_inicio($i18n[69]);
        {
            observacion('', $caja['observacion']);
        }
        contenido_fin();
    }
}
ventana_fin();
