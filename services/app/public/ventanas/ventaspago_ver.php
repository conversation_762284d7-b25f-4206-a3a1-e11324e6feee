<?php

$datos = array_sql(consulta_sql(
    "SELECT ventaspagos.idventapago, ventaspagos.idnumeroventapago, ventaspagos.total, ventaspagos.fecha, ventaspagos.closed_at, ventaspagos.updated_at, ventaspagos.idventa, ventaspagos.tiporelacion, ventaspagos.MP_operation_id, ventaspagos.MP_operation_id, ventaspagos.idcliente, ventaspagos.observacion, ventaspagos.idrelacion,
        usuarios.nombre AS usuario,
        tablas_formasdepago.nombre AS formapago,
        movimientosxcajas.idcaja,
        categorias_conceptos.padres, categorias_conceptos.idconceptopadre, categorias_conceptos.nombre AS concepto,
        categorias_cajas.nombre AS caja,
        ventas.numero,
        categorias_ventas.letra, categorias_ventas.puntodeventa,
        monedas.simbolo, monedas.idmoneda,
        (SELECT cotizaciones.cotizacion FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND cotizaciones.fechayhora = (SELECT MAX(fechayhora) FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND fechayhora < ventaspagos.fecha)) AS cotizacion_anterior
    FROM ventaspagos
        LEFT JOIN usuarios ON ventaspagos.idusuario = usuarios.idusuario
        LEFT JOIN tablas_formasdepago ON ventaspagos.idformapago = tablas_formasdepago.idformapago
        LEFT JOIN movimientosxcajas ON (ventaspagos.idventapago = movimientosxcajas.idrelacion AND movimientosxcajas.tiporelacion = 'clientepago')
        LEFT JOIN categorias_conceptos ON movimientosxcajas.idconcepto = categorias_conceptos.idconcepto
        LEFT JOIN cajas ON movimientosxcajas.idcaja = cajas.idcaja
        LEFT JOIN categorias_cajas ON cajas.idtipocaja = categorias_cajas.idtipocaja
        LEFT JOIN ventas ON ventaspagos.idventa = ventas.idventa
        LEFT JOIN categorias_ventas ON categorias_ventas.idtipoventa = ventas.idtipoventa
        LEFT JOIN monedas ON ventaspagos.idmoneda = monedas.idmoneda
    WHERE idventapago = '$id' AND movimientosxcajas.tiporelacion = 'clientepago'
    LIMIT 1"));

$cliente = array_sql(consulta_sql(
    "SELECT clientes.*,
        categorias_localidades.nombre AS localidad
    FROM clientes
        LEFT JOIN categorias_localidades ON clientes.idlocalidad = categorias_localidades.idlocalidad
    WHERE idcliente = '".$datos['idcliente']."'
    LIMIT 1"));

ventana_inicio($i18n[96].$nombre, '100', array(
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[103])));
{
    // Datos básicos
    contenido_inicio($i18n[34], '50');
    {
        if ($datos['idventa'])
            texto('texto', $i18n[104], $datos['letra'].completar_numero($datos['puntodeventa'], 5).'-'.completar_numero($datos['numero'], 8), 'auto', 'ventas.php?a=ver&id='.$datos['idventa']);
        else
            texto('texto', $i18n[104], $i18n[163]);
        texto('moneda', $i18n[74], $datos['total'], false, false, false, false, false, $datos['simbolo']);
        if ($datos['idmoneda'] != 1)
            texto('moneda', $i18n_funciones[330], $datos['cotizacion_anterior'], 'auto');
        $title_fechas =
            ($datos['closed_at'] != '0000-00-00 00:00:00'
                ? $i18n[398] . ':<br>' . mostrar_fecha('fechayhora', $datos['closed_at']) . '<br>' : '')
            . (($datos['updated_at'] != '0000-00-00 00:00:00' && $datos['updated_at'] != $datos['closed_at'])
                ? $i18n[396] . ':<br>' . mostrar_fecha('fechayhora', $datos['updated_at']) : '');
        texto('fechayhora', $i18n[52], $datos['fecha'], 'auto', false, 'info', false, ['title' => $title_fechas]);
        texto('texto', $i18n[81], $datos['formapago']);
        texto('texto', $i18n[23], $datos['caja'], 'auto', 'cajas.php?a=ver&id='.$datos['idcaja']);
        if ($datos['padres']) {
            while ($datos['padres']) {
                $resultado_sql = consulta_sql("SELECT nombre, idconceptopadre, padres FROM categorias_conceptos WHERE idconcepto = '".$datos['idconceptopadre']."' LIMIT 1");
                $datos['concepto'] = campo_sql($resultado_sql, 0, 'nombre').' > '.$datos['concepto'];
                $datos['idconceptopadre'] = campo_sql($resultado_sql, 0, 'idconceptopadre');
                $datos['padres'] = campo_sql($resultado_sql, 0, 'padres');
            }
        }
        texto('texto', $i18n[146], $datos['concepto']);
        texto('texto', $i18n[51], $datos['usuario']);

        if ($_SESSION['modulo_ML'] && $datos['MP_operation_id'] && contar_sql(consulta_sql("SELECT MP_estado FROM tienda WHERE MP_estado = '1' LIMIT 1"))) {
            texto('url', $i18n[183], $datos['MP_operation_id'], 'auto', 'https://www.mercadopago.com.ar/activities');
        }
    }
    contenido_fin();

    // Cliente
    contenido_inicio($i18n[35], '50');
    {
        texto('texto', $i18n[39], $cliente['nombre'], 'auto', 'clientes.php?a=ver&id='.$cliente['idcliente'], 'cambiar', false, $opciones[] = array('tipo' => 'flotante', 'url' => 'cambiar_clientes_pagos', 'title' => $i18n[338]));
        entrada('hidden', 'idcliente', '', $cliente['idcliente']);
        texto('texto', $i18n[36], $cliente['idcliente']);
        texto('texto', $i18n[40], $cliente['contacto']);
        texto('mail', $i18n[42], $cliente['mail']);
        texto('texto', $i18n[41], $cliente['telefonos']);
        texto('texto', $i18n[43], $cliente['domicilio']);
        texto('texto', $i18n[148], $cliente['localidad']);
    }
    contenido_fin();

    salto_linea();

    // Cheques
    if ($datos['tiporelacion'] == 'cheque')
    {
        $cheques = array_sql(consulta_sql("SELECT * FROM cheques WHERE idcheque = '".$datos['idrelacion']."' LIMIT 1"));
        $banco = campo_sql(consulta_sql("SELECT nombre FROM tablas_bancos WHERE idbanco = '".$cheques['idbanco']."' LIMIT 1"));
        contenido_inicio($i18n[269], '100');
        {
            texto('texto', $i18n[270], ($cheques['tipo'] == 'tercero' ? $i18n[288] : $i18n['propio']));
            texto('fecha', $i18n[271], $cheques['fechacobro']);
            texto('texto', $i18n[253], $banco);
            texto('texto', $i18n[255], $cheques['titular']);
            texto('texto', $i18n[254], $cheques['numero']);
            $fecha = campo_sql(consulta_sql("SELECT fecha FROM mensajes WHERE idmensaje = '".$cheques['idmensaje']."'"));
            if ($fecha)
                texto('fecha', $i18n[256], $fecha);
            else
                texto('texto', $i18n[256], $i18n[289]);        }
        contenido_fin();
    }

    // Retenciones
    if ($datos['tiporelacion'] == 'retencion')
    {
        $retencion = array_sql(consulta_sql(
            "SELECT retenciones.observacion,
                categorias_tributos.nombre AS tributo
            FROM retenciones
                LEFT JOIN categorias_tributos ON categorias_tributos.idtributo = retenciones.idtributo
            WHERE idretencion = '".$datos['idrelacion']."'
            "));
        contenido_inicio($i18n[257], '100');
        {
            texto('texto', $i18n[258], $retencion['tributo']);
            texto('texto', $i18n[259], $retencion['observacion']);
        }
        contenido_fin();
    }

    // Observaciones
    if ($datos['observacion']) {
        contenido_inicio($i18n[46]);
        {
            observacion('', $datos['observacion']);
        }
        contenido_fin();
    }
}
ventana_fin();
