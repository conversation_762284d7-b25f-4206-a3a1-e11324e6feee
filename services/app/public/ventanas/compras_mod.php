<?php
$datos = array();

// Si ya fue eliminada la compra (copiado de ventas mod)
if (!array_sql(consulta_sql("SELECT idcompra FROM compras WHERE idcompra = '$id'")))
    ir_inicio($i18n[287]);

// Si mueve saldo y es una cotización anterior, no se puede modificar
if ($_SESSION['modulo_multimoneda'] && es_cotizacion_anterior($modulo, $id))
    ir_ahora('compras.php?a=ver&id='.$id, $i18n[378]);

if (recibir_variable('act_costos', true)) {

    $compra = array_sql(consulta_sql(
        "SELECT estado, total, idproveedor, tiporelacion, idrelacion, operacioninversa
        FROM compras WHERE idcompra = '$id'"));

    comprobantes_actualizar_costos_compra($id);
    comprobantes_recalculando($id);

    if ($compra['estado'] == 'cerrado') {
        $total_final = campo_sql(consulta_sql("SELECT total FROM compras WHERE idcompra = '$id'"));

        $buscar_monedas = [
            'proveedores' => $compra['idproveedor'],
            'compras' => $id,
        ];
        $idmonedas = idmonedas($buscar_monedas);

        $diferencia = ($compra['operacioninversa'] ? -1 : 1) * ($total_final - $compra['total']);
        actualizar_saldo('compras', $id, $diferencia);
        actualizar_saldo('proveedores', $compra['idproveedor'], cotizacion($idmonedas['proveedores'], $idmonedas['compras'], $diferencia));
        if ($compra['tiporelacion'] == 'servicio') {
            actualizar_saldo('comprasxservicios', $compra['idrelacion'], cotizacion($idmonedas['proveedores'], $idmonedas['compras'], $diferencia));
        }
    }

} else {
    comprobantes_recalculando($id);

}

if ($boton)
    $datos = recibir_matriz(array('idcaja', 'idconcepto', 'situacion', 'idformapago', 'condicionventa', 'idusuario', 'numerocompleto', 'fecha', 'periodoimputacion', 'observacion', 'tributos', 'muevesaldo', 'muevestock', 'agregarpago', 'vencimiento1', 'vencimiento2', 'recordatorios', 'numero', 'letra', 'puntodeventa', 'modo_ajuste', 'iddeposito', 'idlocalidad', 'idmoneda'));

$datos = inicializar($boton, $id, $datos);

switch ($boton) {
    case $i18n[16]: //Cerrar
        $compra = obtener_operacion($id);
        actualizar_operacion($datos);
        extras_mod();

        if (validar_cierre($compra, $datos)) {
            cerrar_compra($compra, $datos);
            ir_ahora('compras.php?a=ver&id='.$id);
        }
        break;

    case $i18n[18]: //Anular
        $compra = inicializar_compra($id);
        if (validar_compra($compra, $datos)) {
            $datos = anular_compra($compra, $datos);
            actualizar_operacion($datos);
            extras_mod();

            ir_ahora('compras.php?a=ver&id='.$id);

        } else {
            actualizar_operacion($datos);
        }
        break;

    case $i18n[14]: //Dejar abierta
    case $i18n_funciones[22]: //Aceptar
        $compra = inicializar_compra($id);
        if (validar_compra($compra, $datos)) {
            $datos = aceptar_compra($compra, $datos);
            actualizar_operacion($datos);
            extras_mod();

            ir_atras();

        } else {
            actualizar_operacion($datos);
            extras_mod();
        }
        break;
}

$compra = inicializar_compra($id);
if (!$compra['iddeposito']) $compra['iddeposito'] = 1;

$proveedor = inicializar_proveedor($compra['idproveedor']);

mensajes_efimeros();

// Alertas de modificación de compras cerradas
if ($compra['estado'] == 'cerrado' && $compra['muevestock'] && $compra['muevesaldo']) {
    script_flotante('informacion', $i18n[367], '10000');
} else if ($compra['estado'] == 'cerrado') {
    if ($compra['muevestock'])
        script_flotante('informacion', $i18n[372], '10000');
    if ($compra['muevesaldo'])
        script_flotante('informacion', $i18n[373], '10000');
}

$temp_confirma = $i18n[19].'\n';
if ($compra['estado'] == 'abierto') {
    $temp_confirma.= '\n'.$i18n[27];
} elseif ($compra['estado'] == 'cerrado') {
    if ($compra['muevesaldo'])
        $temp_confirma.= '\n'.$i18n[134].'\n'.$i18n[137];
    if ($compra['muevestock'])
        $temp_confirma.= '\n'.$i18n[135];
}
if (!$boton && $datos['condicion'] == 'contado' && !$compra['condicionventa'])
    $compra['condicionventa'] = 'contado';

if($proveedor['cuit'] && !$proveedor['razonsocial']){
    script_flotante('alerta', $i18n[311], '10000');
    $proveedor['razonsocial'] = $proveedor['nombre'];
}

ventana_inicio($i18n[84].$compra['tipocompra'].' '.strtoupper($compra["letra"]).$compra["nro_compra"], '100', array(
    array('tipo' => 'imagen', 'url' => 'compras.php?a=ver&id='.$compra['idcompra'], 'a' => 'ver', 'title' => $i18n[23]),
    array('tipo' => 'imagen', 'url' => 'compras.php?a=baja&id='.$compra['idcompra'], 'a' => 'baja', 'title' => $i18n[61], 'permiso' => 'compras_baja', 'opciones' => 'onclick="return confirma('."'".$temp_confirma."'".')"'),
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[62])));
{
    // Datos básicos
    contenido_inicio($i18n[34], '50');
    {
        texto('texto', $i18n[122], $i18n[$compra['estado']]);
        texto('texto', $i18n[49], $compra['tipocompra']);
        $tipocompra = $compra['tipocompra'];
        if ($compra['esfiscal']) {
            if ($compra['numerocompleto'] && !$compra['numero'])
                texto('texto', $i18n[73], $compra['numerocompleto']);

            selector_array('letra', $i18n[202], false, '20', array(
                array('id'=>'A','valor'=>'A'),
                array('id'=>'B','valor'=>'B'),
                array('id'=>'C','valor'=>'C'),
                array('id'=>'M','valor'=>'M')),
                '', 'onchange="recargar_formulario('."'$i18n[302]', '$modulo', '$tipocompra', ".$id.', $(this).val())"'
            );

            entrada('numeros', 'puntodeventa', $i18n[203], $compra['puntodeventa'], '40', '5');
            entrada('numeros', 'numero', $i18n[50], $compra['numero'], '40', '9');

            // Armo el período de imputación
            $temp_array = explode('-', $compra['fechaimputacion']);
            $hoy = date("Y-m-d");
            $fechaimputacion_fin = date('Y-m-d', strtotime($compra['fechaimputacion']. ' + 1 years'));
            $periodoimputacion = $temp_array[1].'-'.$temp_array[0];
            if ($_SESSION['configuracion_discrimina']) {
                $meses = listar_meses(date("Y-m-d", time() - 86400 * 365), date("Y-m-d", time() + 86400 * 30));
                $array_selector_opciones = array();
                for ($i = count($meses); $i; $i--) {
                    $mes = $meses[$i-1];
                    $array_selector_opciones[] = array('id' => $mes, 'valor' => $mes . ' ('.$i18n_funciones['mes_'.$mes[0].$mes[1]].')');
                }
                entrada('fechayhora', 'fecha', $i18n[52], $compra['fecha'], '50');
                if (date("Y-m", strtotime($hoy)) > date("Y-m", strtotime($fechaimputacion_fin))) {
                    texto('texto', $i18n[209], substr(implode('-',array_reverse(explode('-', $compra['fechaimputacion']))), 3, 7));
                    entrada('hidden', 'periodoimputacion', false, $periodoimputacion);
                } else {
                    selector_array('periodoimputacion', $i18n[209], $periodoimputacion, '50', $array_selector_opciones);
                }

            } else {
                entrada('fechayhora', 'fecha', $i18n[52], $compra['fecha'], '100');
                entrada('hidden', 'periodoimputacion', false, $periodoimputacion);
            }

        } else {
            entrada('texto', 'numerocompleto', $i18n[50], $compra['numerocompleto'], 'auto', '60');
            entrada('fechayhora', 'fecha', $i18n[52], $compra['fecha'], 'auto');
        }

        // Condición de venta
        selector_array('condicionventa', $i18n[65], $compra['condicionventa'], 'auto', array(
            array('id' => 'contado', 'valor' => $i18n['contado']),
            array('id' => 'cuentacorriente', 'valor' => $i18n['cuentacorriente']),
            array('id' => 'debito', 'valor' => $i18n['debito']),
            array('id' => 'credito', 'valor' => $i18n['credito']),
            array('id' => 'cheque', 'valor' => $i18n['cheque']),
            array('id' => 'ticket', 'valor' => $i18n['ticket']),
            array('id' => 'otra', 'valor' => $i18n['otra'])
        ), false, 'onchange="revisarAgregarPago()"');
    }
    contenido_fin();

    // Proveedor
    contenido_inicio($i18n[35], '50');
    {
        // Si es Sin Especificar, dejamos que cargue los datos:
        if ($proveedor['idproveedor'] == 1) {
            texto('texto', $i18n[39], $proveedor['nombre'], 'auto', false, 'cambiar', false, $opciones[] = array('tipo' => 'flotante', 'url' => 'cambiar_proveedores'));
            bloque_inicio('compras_tipoiva');
                texto('texto', $i18n[63], $compra['tipoiva']);
            bloque_fin();
            bloque_inicio('compras_razonsocial');
                texto('texto', $i18n[37], ($compra['razonsocial'] ? $compra['razonsocial'] : $i18n_funciones[24]));
            bloque_fin();
            bloque_inicio('compras_cuit');
                texto('texto', $i18n[38], ($compra['cuit'] ? mostrar_cuit($compra['cuit']) : ' '));
            bloque_fin();
            entrada('hidden', 'idlocalidad', false, $compra['idlocalidad']);
            bloque_inicio('compras_localidad');
                texto('texto', $i18n[142], $compra['localidad']);
            bloque_fin();
            bloque_inicio('compras_cuit');
                enlaces(false, array(
                    array('tipo' => 'modal', 'modulo' => 'compras',
                        'url' => 'compras_cuit', 'id' => $id, 'valor' => $i18n[289])
                    ));
            bloque_fin();
        } else {
            texto('texto', $i18n[39], $proveedor['nombre'], 'auto', false, 'cambiar', false, $opciones[] = array('tipo' => 'flotante', 'url' => 'cambiar_proveedores'));
            entrada('hidden', 'idproveedor', '', $proveedor['idproveedor']);
            entrada('hidden', 'idlocalidad', false, $proveedor['idlocalidad']);
            texto('texto', $i18n[63], $proveedor['tipoiva']);
            texto('texto', $i18n[37], $proveedor['razonsocial']);
            texto('cuit', $i18n[38], $proveedor['cuit']);
            texto('texto', $i18n[43], $proveedor['domicilio']);
            texto('texto', $i18n[142], $proveedor['localidad']);
        }
    }
    contenido_fin();

    salto_linea();

    // Datos adicionales
    contenido_inicio($i18n[72], '50', true, false, false, 'id="datos_adicionales"');
    {
        entrada('hidden', 'idusuario', '', $compra['idusuario']);
        texto('texto', $i18n[51], $compra['usuario']);
        // Situación
        $situaciones = array(
            array('id' => 'sin_especificar', 'valor' => $i18n['sin_especificar']),
            array('id' => 'pendiente', 'valor' => $i18n['pendiente']),
            array('id' => 'aprobado', 'valor' => $i18n['aprobado']),
            array('id' => 'rechazado', 'valor' => $i18n['rechazado'])
        );
        selector_array('situacion', $i18n[155], $compra['situacion'], 'auto', $situaciones);
        // Vencimientos
        entrada('fecha', 'vencimiento1', $i18n[85], $compra['vencimiento1'], '50');
        bloque_inicio('div_vencimiento2', ($compra['vencimiento1'] != '0000-00-00' ? '' : 'style="display: none;"'));
        {
            entrada('fecha', 'vencimiento2', $i18n[86], $compra['vencimiento2'], '50');
            marcas('', '100', array(array('titulo' => $i18n[32], 'nombre' => 'recordatorios', 'valor' => $datos['recordatorios'], 'ayuda_puntual' => $i18n[140], 'opciones' => ($compra['estado'] != 'abierto' ? 'disabled="disabled"' : ''))));
        }
        bloque_fin();
    }
    contenido_fin();

    // Moneda
    contenido_inicio($i18n[374], '50', true);
    {
        if ($_SESSION['modulo_multimoneda']) {
            if ($compra['estado'] == 'abierto') {
                selector('idmoneda', $i18n[374], $compra['idmoneda'], 'auto', 'monedas', 'idmoneda', true, false, false, false, 'onchange="guardar_moneda('."'$modulo', ".$id.', $(this).val())"');
                if ($compra['idmoneda'] != 1)
                    texto('italica', '', $i18n_funciones[331].' $ '.cotizacion(1, $compra['idmoneda'], 1), 'auto', false, false, false, ['string' => 'id="cotizacion_actual"']);

            } else {
                texto('titulo', $i18n[374], $compra['moneda'].' ('.$compra['simbolo'].')', 'auto');
                entrada('hidden', 'idmoneda', '', $compra['idmoneda']);
            }
        } else {
            texto('titulo', $i18n[374], $i18n[375], '30');
            entrada('hidden', 'idmoneda', '', 1);
        }
    }
    contenido_fin();

    salto_linea();

    // Movimientos generados
    contenido_inicio($compra['estado'] == 'abierto' ? $i18n[230] : $i18n[220], '50', true, false, false, 'id="movimientos_generados"');
    {
        comprobantes_movimientos($compra, true); // Siempre editable si está abierto
    }
    contenido_fin();

    // Depósitos
    contenido_inicio($i18n[303], '50', true, false);
    {
        entrada('hidden', 'iddeposito_origen', false, $compra['iddeposito']);
        if ($compra['estado'] == 'abierto')
            selector('iddeposito', $i18n[304], $compra['iddeposito'], 'auto', 'depositos', 'iddeposito', false, '', false, $i18n[305], 'onchange="guardar_sucursal('."'iddeposito', '$modulo', ".$id.', $(this).val())"');
        else
            texto('texto', $i18n[304], $compra['deposito']);
    }
    contenido_fin();

    extras();

    // Seleccionador de productos
    contenido_inicio($i18n[53], '100', false, false, false, ($_SESSION['mobile'] ? 'style="overflow-x: scroll;"' : ''));
    {
        seleccionador_productos('compras', $compra['discrimina'], '1', $compra['iddeposito']);
    }
    contenido_fin();

    // Observaciones
    contenido_inicio($i18n[46], ($compra['discrimina'] == 'R' ? '100': '66'));
    {
        area('observacion', false, $compra['observacion']);
    }
    contenido_fin();

    // Totales
    if ($compra['discrimina'] != 'R') {
        contenido_inicio(false, '33', false, false, false, ['id' => "totales", 'class' => "desglose"]);
        {
            enlaces(false, array(
                array('tipo' => 'modal', 'modulo' => $modulo, 'url' => 'comprobantes_mod_descuento', 'id' => $id, 'valor' => $i18n[196]),
                array('tipo' => 'enlace', 'modulo' => $modulo,
                    'url' => 'compras.php?a=mod&id='.$id."&act_costos=1",
                    'id' => $id, 'valor' => $i18n[376],
                    'opciones' => 'onclick="return confirma('."'".$i18n[377]."'".')"'),
                ));
            enlaces(false, array(
                array('tipo' => 'modal', 'modulo' => $modulo, 'url' => 'tributos', 'id' => $id, 'valor' => $i18n[201]),
                array('tipo' => 'flotante', 'modulo' => $modulo, 'url' => 'comprobantes_ver_totales', 'id' => $id, 'valor' => $i18n[195]),
                ));

            comprobantes_mostrar_totales($compra, $compra['discrimina']);
        }
        contenido_fin();
    }
    salto_linea();

    // Orden de pago
    if ($compra['estado'] == 'abierto'
        && $_SESSION['perfil_cajas_ver']
        && $_SESSION['perfil_compraspagos_alta']
        && !$compra['operacioninversa']) {

        contenido_inicio($i18n[117], '50', false, false, false, 'id="altapago"');
        {
            marcas('', 'auto', array(array('nombre' => 'agregarpago', 'titulo' => $i18n[113], 'valor' => $datos['agregarpago'], 'opciones' => 'id="agregarpago" onclick="revisarAgregarPago()"')));
            bloque_inicio('detallepago');
            {
                if ($_SESSION['perfil_idperfil'] == '1')
                    $cajas_sql = consulta_sql("SELECT * FROM categorias_cajas
                        INNER JOIN cajas ON categorias_cajas.idcaja = cajas.idcaja
                        WHERE estado = '1'
                        AND cajas.fechacierre = '0000-00-00 00:00:00'
                    ");
                else
                    $cajas_sql = consulta_sql("SELECT * FROM categorias_cajas
                                                LEFT JOIN perfilesxcajas ON categorias_cajas.idtipocaja = perfilesxcajas.idtipocaja
                                                INNER JOIN cajas ON categorias_cajas.idcaja = cajas.idcaja
                                                WHERE estado = '1'
                                                AND cajas.fechacierre = '0000-00-00 00:00:00'
                                                AND (categorias_cajas.compartida = '1' OR (idperfil = '".$_SESSION['perfil_idperfil']."' AND alta='1'))");
                $cajas = array();
                $formasdepago_disponibles = array();
                while ($array_tipo_caja = array_sql($cajas_sql)) {
                    $cajas[] = array(
                        'id' => $array_tipo_caja['idcaja'],
                        'valor' => $array_tipo_caja['nombre'],
                        'datasets' => array(
                            'data-tipoformapago' => $array_tipo_caja['tipo'],
                            )
                    );
                    if (!in_array($array_tipo_caja['tipo'], $formasdepago_disponibles))
                        $formasdepago_disponibles[] = "'".$array_tipo_caja['tipo']."'";
                }

                $formasdepago = array();
                if (count($formasdepago_disponibles)) {
                    $formasdepago_sql = consulta_sql("SELECT *
                        FROM tablas_formasdepago
                        WHERE tipo IN (".implode(',', $formasdepago_disponibles).")");
                    while ($formadepago = array_sql($formasdepago_sql)) {
                        $formasdepago[] = array(
                            'id' => $formadepago['idformapago'],
                            'valor' => $formadepago['nombre'],
                            'datasets' => array(
                                'data-tipoformapago' => $formadepago['tipo'])
                            );
                    }
                    ?>
                        <script type="text/javascript" charset="utf-8">
                            $(function() {
                                $("select[name='idformapago'] option[data-tipoformapago='cheque']").remove();
                                $("select[name='idformapago'] option[data-tipoformapago='retencion']").remove();
                                $("select[name='idcaja'] option[data-tipoformapago='cheque']").remove();
                                $("select[name='idcaja'] option[data-tipoformapago='retencion']").remove();
                            });
                        </script>
                    <?php
                    $ayuda_puntual = contar_sql($cajas_sql) ? $i18n[25] : $i18n[105];
                    selector_array('idformapago', $i18n[81], $datos['idformapago'], '33', $formasdepago, false, 'onchange="seleccionar_tipo_caja()"');
                    selector_array('idcaja', $i18n[24], $datos['idcaja'], '33', $cajas, $ayuda_puntual);
                    selector_familiar('idconcepto', $i18n[26], $datos['idconcepto'], '33', 'categorias_conceptos', true, true, true);

                } else {
                    texto('italica', false, $i18n[114], 'auto', false, 'alerta');
                }

            }
            bloque_fin();
        }
        contenido_fin();
    }

    // Actualizar costos
    if ($compra['estado'] == 'abierto'
        && $_SESSION['perfil_productos_mod']
        && $compra['tipocompra'] != $i18n[286]) {

        contenido_inicio($i18n[204], '50', false, false, false, ' id="actualizar_costos" style="float: right;  margin-right: 15px; "');
        {
            $temp_selector_modo_ajuste = array(
                array('id' => '', 'valor' => $i18n[205]),
                array('id' => 'costo_precio', 'valor' => $i18n[206]),
                array('id' => 'costo_utilidad', 'valor' => $i18n[207]),

            );
            selector_array('modo_ajuste', $i18n[208], false, 'auto', $temp_selector_modo_ajuste, $i18n[120]);
        }
        contenido_fin();
    }

    $temp_botones = array();
    if ($compra['estado'] == 'abierto') {
        $temp_botones[] = array('valor' => $i18n[16]);
        $temp_botones[] = array('valor' => $i18n[14]);
    } else {
        $temp_botones[] = array('valor' => $i18n_funciones[22]);
    }
    if ($compra['estado'] != 'anulado')
        $temp_botones[] = array('valor' => $i18n[18]);
    botones($temp_botones);
}
ventana_fin();

?>
<script charset="utf-8">
    function validacion_compras_mod(boton)
    {
<?php
if ($compra['estado'] == 'abierto') {
    echo '
        if (boton == "'.$i18n[14].'") {
            return confirma("'.$i18n[22].'\n\n'.$i18n[27].'");
        } else if (boton == "'.$i18n[18].'") {
            return confirma("'.$i18n[20].'\n\n'.$i18n[27].'");
        }';
} elseif ($compra['estado'] == 'cerrado') {
    //Aceptar
    /*$temp_confirma = $i18n[21].'\n';
    if ($compra['muevesaldo'])
        $temp_confirma.= '\n'.$i18n[134].'\n'.$i18n[136];
    if ($compra['muevestock'])
        $temp_confirma.= '\n'.$i18n[135];
    echo '
        if (boton == "'.$i18n_funciones[22].'") {
            return confirma("'.$temp_confirma.'");
        }';
    */
    //Anular
    $temp_confirma = $i18n[20];
    if ($compra['muevesaldo'])
        $temp_confirma.= '\n'.$i18n[134].'\n'.$i18n[136];
    if ($compra['muevestock'])
        $temp_confirma.= '\n'.$i18n[131];
    echo '
        if (boton == "'.$i18n[18].'") {
            return confirma("'.$temp_confirma.'");
        }';
}
?>

        return true;
    }
    $(function() {
        $("select[name=letra]").val("<?php echo $compra['letra']?>");
        revisarAgregarPago();
    });
</script>
