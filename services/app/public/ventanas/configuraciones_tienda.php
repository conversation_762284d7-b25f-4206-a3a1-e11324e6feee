<?php
if ($boton) {
    $datos = recibir_matriz(array('API_estado', 'tienda_idtipoventa', 'tienda_sinstock', 'puntodeventa', 'idtipocliente'));
    $datos['tienda_estado'] = $datos['API_estado'];
    $datos['tienda_idtipocliente'] = $datos['idtipocliente'];
} else {
    $datos = array_sql(consulta_sql("SELECT * FROM tienda LIMIT 1"));
    if (!$datos['API_secret']) {
        $datos['API_secret'] = generar_random(32);
        consulta_sql("UPDATE tienda SET API_secret = '".$datos['API_secret']."' LIMIT 1");
    }
    if (!$datos['tienda_nombre']) {
        $datos['tienda_nombre'] = url_amigable($_SESSION['empresa_nombre']);
        consulta_sql("UPDATE tienda SET tienda_nombre = '".$datos['tienda_nombre']."' LIMIT 1");
    }

    $selector_punto_de_venta[] = array('id' => 0, 'valor' => $i18n[245]);
    $resultado_sql = consulta_sql("SELECT puntodeventa FROM categorias_ventas WHERE estado = '1'");
    while ($tipoventa = array_sql($resultado_sql)) {
        if (array_search($tipoventa['puntodeventa'], array_column($selector_punto_de_venta, 'id')) === false ) {
            $selector_punto_de_venta[] = array('id' => $tipoventa['puntodeventa'], 'valor' => $i18n[193].' '. $tipoventa['puntodeventa']);
        }
    }
}

switch ($boton) {
    case $i18n_funciones[22]: // Aceptar

        // if ($datos['tienda_nombre'] && contar_sql(consulta_sql("SELECT idempresa FROM tiendas WHERE nombre = '".$datos['tienda_nombre']."' AND idempresa != '".$_SESSION['empresa_idempresa']."' LIMIT 1", 'saasargentina'))) {
        //     mensajes_alta($i18n[372]);

        // } else {

            // Si es la primera vez que entran a configurar la tienda y no seleccionan un tipo de venta existente se crea uno nuevo
            global $bd_link;
            if (!$datos['tienda_idtipoventa']) {
                consulta_sql("INSERT INTO categorias_ventas SET
                        idcomportamiento = '103',
                        nombre = 'Pedido desde Tienda online',
                        letra = 'P',
                        puntodeventa = '1',
                        ultimonumero = '0',
                        muevestock = '',
                        muevesaldo = '1',
                        operacioninversa = '',
                        tienesituacion = '1',
                        situacion = 'pendiente',
                        auto_aprobar = '1',
                        tipofacturacion = 'interno',
                        tipoimpresion = 'predeterminado',
                        discrimina = '".($_SESSION['configuracion_idtipoiva'] == '1' ? 'B' : 'C')."'
                    ");
                $datos['tienda_idtipoventa'] = id_sql($bd_link);
            }
            consulta_sql("UPDATE tienda SET
                    tienda_estado = '".$datos['tienda_estado']."',
                    tienda_pausa = '".$datos['tienda_pausa']."',
                    tienda_nombre = '".$datos['tienda_nombre']."',
                    tienda_idtipoventa = '".$datos['tienda_idtipoventa']."',
                    tienda_sinstock = '".$datos['tienda_sinstock']."',
                    API_estado = '".$datos['API_estado']."',
                    tienda_idtipocliente = '".$datos['tienda_idtipocliente']."'
                LIMIT 1");

            // consulta_sql("UPDATE variables SET valor = '".$datos['puntodeventa']."' WHERE variable = 'puntodeventa_predeterminado_api_facturacion_masiva'");

            // Tengo que consultar primero si existe porque no puedo usar otro método ya que consulta_sql cierra la conexión con saasargentina
            if (contar_sql(consulta_sql("SELECT idempresa FROM tiendas WHERE idempresa = '".$_SESSION['empresa_idempresa']."' LIMIT 1", 'saasargentina')))
                consulta_sql("UPDATE tiendas SET nombre = '".$datos['tienda_nombre']."' WHERE idempresa = '".$_SESSION['empresa_idempresa']."' LIMIT 1", 'saasargentina');
            else
                consulta_sql("INSERT INTO tiendas SET nombre = '".$datos['tienda_nombre']."', idempresa = '".$_SESSION['empresa_idempresa']."'", 'saasargentina');
            ir_atras();
        // }
        break;

    case $i18n_funciones[23]: // Cancelar
        ir_atras();
        break;
}

$puntodeventa = campo_sql(consulta_sql("SELECT valor FROM variables WHERE variable = 'puntodeventa_predeterminado_api_facturacion_masiva'"));

ventana_inicio($i18n[147]);
{
    // Tienda online
    contenido_inicio($i18n[89], '50');
    {
        marcas('', 'auto', array(
            // array('nombre' => 'tienda_estado', 'titulo' => $i18n[150], 'valor' => $datos['tienda_estado']),
            array('nombre' => 'API_estado', 'titulo' => $i18n[152], 'valor' => $datos['API_estado'], 'ayuda_puntual' => $i18n[153]),
            // array('nombre' => 'tienda_avanzada', 'titulo' => $i18n[370], 'valor' => $datos['tienda_avanzada']),
            ));

        // salto_linea();
        // bajo_linea();
        // bloque_inicio('tienda_activada', !$datos['tienda_estado'] ? 'style="display: none;"' : '');
        // {
        //     texto('url', $i18n[366], URL_TIENDA.'/'.$datos['tienda_nombre'], 'auto', URL_TIENDA.'/'.$datos['tienda_nombre'], false, false, 'id="tienda_nombre"');
        // }
        // bloque_fin();

        bloque_inicio('API_activada', !$datos['API_estado'] ? 'style="display: none;"' : '');
        {
            salto_linea();
            bajo_linea();

            texto('texto', $i18n[367], $_SESSION['empresa_iue'], 'auto', false, false, $i18n[368]);
            // texto('texto', $i18n[154], $datos['API_secret'], 'auto', false, false, $i18n[380]);
            texto('italica', '', $i18n[151]);
        }
        bloque_fin();
    }
    contenido_fin();

    bloque_inicio('tienda_avanzada', !$datos['API_estado'] ? 'style="display: none;"' : '');
    {
        contenido_inicio('Datos avanzados', '50');
        {
            // salto_linea();
            // bajo_linea();

            if (!$datos['tienda_idtipoventa'])
                $array_selector_opciones = array(array('id' => 0, 'valor' => 'Pedido desde Tienda online'));
            else
                $array_selector_opciones = array();
            $resultado_sql = consulta_sql(
                "SELECT idtipoventa, nombre
                FROM categorias_ventas
                WHERE tipofacturacion = 'interno'
                    AND operacioninversa = '0'");
            while ($temp_array = array_sql($resultado_sql)) {
                $array_selector_opciones[] = array('id' => $temp_array['idtipoventa'], 'valor' => $temp_array['nombre']);
            }
            selector_array('tienda_idtipoventa', $i18n[360], $datos['tienda_idtipoventa'], '50', $array_selector_opciones, $i18n[373]);

            salto_linea();
            bajo_linea();
            marcas($i18n[236], 'auto', array(
                array('nombre' => 'tienda_sinstock', 'titulo' => $i18n[371], 'valor' => $datos['tienda_sinstock']),
                // array('nombre' => 'tienda_pausa', 'titulo' => $i18n[359], 'valor' => $datos['tienda_pausa']),
                ));

            salto_linea();
            bajo_linea();
            selector('idtipocliente', $i18n[46], $datos['tienda_idtipocliente'], '50', 'categorias_clientes', 'nombre', false, true, true);
            observacion(false, $i18n[629], false, 'info');
        }
        contenido_fin();

        salto_linea();

        /* Oculto funcionalidad hasta próxima actualización
        contenido_inicio($i18n[636], '50');
        {
            selector_array('puntodeventa', $i18n[637], $puntodeventa, '50', $selector_punto_de_venta, $i18n[640]);
            texto('italica', false, $i18n[641], false, false, 'info');
        }
        contenido_fin();
        */
    }
    bloque_fin();
    // Estilo de la tienda
    // contenido_inicio($i18n[351], '50', false, false, $i18n[352], 'id="contenido_estilo_tienda" style="display: none;"');
    // {
    //     bloque_inicio('estilo_tienda');
    //     {
    //         if ($datos['estilo_tienda']) {
    //             texto('texto', '', $i18n[284]);
    //             enlaces('', array(
    //                 array('url' => URL_S3.$datos['estilo_tienda'].'/estilo_tienda.css', 'valor' => $i18n[121], 'opciones' => 'target="_blank"'),
    //                 array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'estilo_tienda', 'valor' => $i18n[111]),
    //                 array('tipo' => 'ajax', 'url' => 'configuraciones_estilos_baja', 'id' => 'estilo_tienda', 'valor' => $i18n[112])
    //                 ));

    //         } else {
    //             texto('texto', '', $i18n[283]);
    //             enlaces('', array(
    //                 array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'estilo_tienda', 'valor' => $i18n[110])
    //                 ));
    //         }
    //         salto_linea();
    //         bajo_linea();

    //         entrada('texto', 'url_tienda', $i18n[369], URL_TIENDA.'/', '60', '100', false, 'disabled="disabled"');
    //         entrada('texto', 'tienda_nombre', '<br />', $datos['tienda_nombre'], '40', '60');
    //     }
    //     bloque_fin();
    // }
    // contenido_fin();

    botones(array(array('valor' => $i18n_funciones[22]), array('valor' => $i18n_funciones[23])));
}
ventana_fin();

?>
<script type="text/javascript" charset="utf-8">

// function estilo_tienda()
// {
//     $.ajax(
//     {
//         url: "cargadores/modal.php",
//         type: "post",
//         data: (
//         {
//             modulo: "<?=$modulo?>",
//             ventana: "configuraciones_estilo_tienda"
//         }),
//         success: function(data) {
//             $("#estilo_tienda").html(data);
//         }
//     });
// }

function tienda_avanzada()
{
    // Mientras esté solo la API
    if ($("input[name='API_estado']").is(":checked"))
        $("#tienda_avanzada").fadeIn();
    else
        $("#tienda_avanzada").fadeOut();

    // Para cuando esté la tienda
    // if (!$("input[name='tienda_avanzada']").is(":checked")) {
    //     $("#tienda_avanzada").fadeOut();
    //     $("#contenido_estilo_tienda").fadeOut();
    // } else if ($("input[name='tienda_estado']").is(":checked")) {
    //     $("#tienda_avanzada").fadeIn();
    //     $("#contenido_estilo_tienda").fadeIn();
    // } else if ($("input[name='API_estado']").is(":checked")) {
    //     $("#tienda_avanzada").fadeIn();
    //     $("#contenido_estilo_tienda").fadeOut();
    // }
}

$(function () {

    // <?php echo $datos['tienda_avanzada'] ? 'tienda_avanzada();' : ''; ?>
    tienda_avanzada();

    // $("input[name='tienda_estado']").click(function () {
    //     if ($(this).is(":checked"))
    //         $("#tienda_activada").fadeIn();
    //     else
    //         $("#tienda_activada").fadeOut();
    //     tienda_avanzada();
    // });

    $("input[name='API_estado']").click(function () {
        if ($(this).is(":checked"))
            $("#API_activada").fadeIn();
        else
            $("#API_activada").fadeOut();
        tienda_avanzada();
    });

    // $("input[name='tienda_avanzada']").click(function () {
    //     tienda_avanzada();
    // });

    // $("input[name='tienda_nombre']").change(function () {
    //     $("#tienda_nombre a").html('<?=URL_TIENDA.'/'?>'+$("input[name='tienda_nombre']").val());
    //     $("#tienda_nombre a").attr("href", '<?=URL_TIENDA.'/'?>'+$("input[name='tienda_nombre']").val());
    // });
});
</script>
