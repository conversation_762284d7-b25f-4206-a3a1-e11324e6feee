<?php
$venta = array_sql(consulta_sql(
    "SELECT ventas.*,
        usuarios.nombre AS usuario, tablas_condiciones.nombre AS tipoiva, categorias_ventas.discrimina,
        categorias_localidades.nombre AS localidad,
        listas.nombre AS lista, depositos.nombre AS deposito,
        monedas.idmoneda, monedas.nombre AS moneda, monedas.simbolo, monedas.cotizacion,
        (SELECT cotizaciones.cotizacion FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND cotizaciones.fechayhora = (SELECT MAX(fechayhora) FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND fechayhora < ventas.closed_at)) AS cotizacion_anterior
    FROM ventas
        LEFT JOIN usuarios ON ventas.idusuario = usuarios.idusuario
        LEFT JOIN tablas_condiciones ON ventas.idtipoiva = tablas_condiciones.idtipoiva
        LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
        LEFT JOIN categorias_localidades ON ventas.idlocalidad = categorias_localidades.idlocalidad
        LEFT JOIN listas ON ventas.idlista = listas.idlista
        LEFT JOIN depositos ON ventas.iddeposito = depositos.iddeposito
        LEFT JOIN monedas ON ventas.idmoneda = monedas.idmoneda
    WHERE idventa = '$id'"));
$cliente = array_sql(consulta_sql(
    "SELECT clientes.*,
        tablas_condiciones.nombre AS tipoiva,
        categorias_localidades.nombre AS localidad
    FROM clientes
        LEFT JOIN categorias_localidades ON clientes.idlocalidad = categorias_localidades.idlocalidad
        LEFT JOIN tablas_condiciones ON clientes.idtipoiva = tablas_condiciones.idtipoiva
    WHERE idcliente = '".$venta['idcliente']."'"));
$servicio = $venta['tiporelacion'] == 'servicio' && $venta['idrelacion']
    ? array_sql(consulta_sql(
        "SELECT idservicio, titulo
        FROM servicios
        where idservicio = ".$venta['idrelacion']))
    : null;

$temp_desplegable = comprobantes_habilitados_clientes($venta['idcliente']);

$tipoventa = array_sql(consulta_sql("SELECT * FROM categorias_ventas WHERE idtipoventa='".$venta['idtipoventa']."' LIMIT 1"));
$numeroventa = $tipoventa['letra'].completar_numero($tipoventa['puntodeventa'], 5).'-'.completar_numero($venta['numero'], 8);
$discrimina = $tipoventa['discrimina'];

if ($venta['estado'] == 'abierto' && !$_SESSION['perfil_ventas_alta']) {
    $temp_boton_mod = array('tipo' => 'imagen', 'a' => 'mod_no', 'title' => $i18n[57]);
} elseif (($venta['estado'] == 'cerrado' || $venta['estado'] == 'anulado') && !$_SESSION['perfil_ventas_mod']) {
    $temp_boton_mod = array('tipo' => 'imagen', 'a' => 'mod_no', 'title' => $i18n[58]);
} elseif (($venta['estado'] == 'cerrado' || $venta['estado'] == 'anulado') && $venta['idusuario'] != $_SESSION['usuario_idusuario'] && !$_SESSION['perfil_ventas_mod_todos']) {
    $temp_boton_mod = array('tipo' => 'imagen', 'a' => 'mod_no', 'title' => $i18n[59]);
} else {
    $temp_boton_mod = array('tipo' => 'imagen', 'url' => 'ventas.php?a=mod&id='.$venta['idventa'], 'a' => 'mod', 'title' => $i18n[60]);
}

$temp_confirma = $i18n[19].'\n';
if ($venta['estado'] == 'abierto') {
    if ($tipoventa['muevesaldo'])
        $temp_confirma.= '\n'.$i18n[132].'\n'.$i18n[137];
    if ($tipoventa['muevestock'])
        $temp_confirma.= '\n'.$i18n[133];
} elseif ($venta['estado'] == 'cerrado') {
    if ($tipoventa['muevesaldo'])
        $temp_confirma.= '\n'.$i18n[134].'\n'.$i18n[137];
    if ($tipoventa['muevestock'])
        $temp_confirma.= '\n'.$i18n[135];
}

if ($venta['estado'] == 'abierto')
    $temp_alerta_exportar = $i18n[210];
elseif ($tipoventa['tipofacturacion'] == 'electronico' && $venta['estadocae'] != 'aprobado')
    $temp_alerta_exportar = $i18n[211];
else
    $temp_alerta_exportar = false;

// Proceso para verificar si un cae está correcto
/* NO Lo uso por ahora
if (recibir_variable('verificar', true)) {
    if ($tipoventa['tipofacturacion'] != 'electronico' || $venta['estado'] != 'cerrado' || mb_strlen($venta['cae']) != 14) {
        mensajes_alta($i18n[317], 'Alerta');

    } else if (bloquear_wsfe()) {
        if (rece1_verificar($id))
            mensajes_alta($i18n[318], 'Confirmacion');
        else
            mensajes_alta($i18n[319], 'Alerta');
        desbloquear_wsfe();
    }

}
*/

if ($tipoventa['tipofacturacion'] == 'electronico' && $venta['estadocae'] == 'pendiente')
    antiafip();

if ($venta['estado'] != 'abierto' && $venta['muevesaldo'])
    controlar_saldo('ventas', $id);
$venta = controlar_moneda('ventas', $venta);

mensajes_efimeros();

ventana_inicio($i18n[31].$nombre, '100', array(
    array('tipo' => 'desplegable', 'a' => 'alta_relacionada', 'title' => $i18n[247],
        'url' => 'ventas_alta_auto.php?a=alta&idventa='.$id.($venta['discrimina'] == 'R' ? '&act_precios=1' : ''),
        'desplegable' => $temp_desplegable,
        'desplegable_vacio' => (!count($temp_desplegable) ? $i18n[246].$cliente['tipoiva'] : false)
        ),
    $temp_boton_mod,
    array('tipo' => 'imagen', 'url' => 'ventas.php?a=baja&id='.$venta['idventa'], 'a' => 'baja', 'title' => $i18n[61], 'permiso' => 'ventas_baja', 'opciones' => 'onclick="return confirma('."'".$temp_confirma."'".')"'),
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[62],
        'opciones' => ($temp_alerta_exportar
            ? 'onclick="return alert('."'".$temp_alerta_exportar."'".')"'
            : ''
            ))));
{
    // Datos básicos
    contenido_inicio($i18n[34], '50');
    {
        texto('texto', $i18n[106], $i18n[$venta['estado']], 'auto', false, $venta['estado']);
        texto('texto', $i18n[49], $tipoventa['nombre']);
        if ($tipoventa['tipofacturacion'] == 'electronico' && $venta['estadocae'] != 'aprobado')
            texto('texto', $i18n[50], $numeroventa . $i18n[380], 'auto', false, 'alerta');
        else
            texto('texto', $i18n[50], $numeroventa);
        $title_fechas =
            ($venta['closed_at'] != '0000-00-00 00:00:00'
                ? $i18n[395] . ':<br>' . mostrar_fecha('fechayhora', $venta['closed_at']) . '<br>' : '')
            . (($venta['updated_at'] != '0000-00-00 00:00:00' && $venta['updated_at'] != $venta['closed_at'])
                ? $i18n[396] . ':<br>' . mostrar_fecha('fechayhora', $venta['updated_at']) : '');
        texto('fechayhora', $i18n[52], $venta['fecha'], 'auto', false, 'info', false, ['title' => $title_fechas]);
        texto('texto', $i18n[65], $i18n[$venta['condicionventa']]);
        if ($tipoventa['tipofacturacion'] == 'electronico' && $venta['estadocae'] == 'aprobado') {
            $enlace_verificar = qr_afip($venta, $tipoventa, true);
            texto('url', $i18n[140], $venta['cae'], 'auto', $enlace_verificar);
            texto('fecha', $i18n[144], $venta['vencimientocae']);
            if ($venta['obscae'])
                texto('texto', $i18n[71], $venta['obscae']);

        } else if ($tipoventa['tipofacturacion'] == 'electronico') {
            texto('texto', $i18n[140], ucfirst($venta['estadocae']), 'auto', false, 'alerta');
            if ($venta['obscae'])
                texto('texto', $i18n[72], $venta['obscae']);
        }
        if ($tipoventa['tienesituacion'])
            texto('texto', $i18n[155], ($venta['situacion'] ? $i18n[$venta['situacion']] : $i18n['sin_especificar']), 'auto', false, 'cambiar', false, $opciones[] = array('tipo' => 'flotante', 'url' => 'cambiar_situacion', 'title' => $i18n[382]));
    }
    contenido_fin();

    // Cliente
    contenido_inicio($i18n[35], '50');
    {
        texto('texto', $i18n[39], $cliente['nombre'], 'auto', 'clientes.php?a=ver&id='.$cliente['idcliente'], 'cambiar', false, $opciones[] = array('tipo' => 'flotante', 'url' => 'cambiar_clientes', 'title' => $i18n[315]));
        texto('texto', $i18n[36], $cliente['idcliente']);
        texto('texto', $i18n[43], $venta['domicilio']);
        texto('texto', $i18n[145], $venta['localidad']);
        texto('texto', $i18n[63], $venta['tipoiva']);
        if ($venta['cuit']) {
            texto('texto', $i18n[37], $venta['razonsocial']);
            texto('texto', $i18n[38], $venta['cuit']);
        } else if ($venta['dni']) {
            texto('texto', $i18n[37], $venta['razonsocial']);
            texto('texto', $i18n[967], tipoDocs($venta['tipodoc']));
            texto('texto', $i18n[242], $venta['dni']);
        }
    }
    contenido_fin();

    salto_linea();

    // Datos adicionales
    contenido_inicio($i18n[73], '50', true);
    {
        texto('texto', $i18n[392], $venta['usuario']);
        if ($venta['vencimiento1'] != '0000-00-00')
            texto('fecha', $i18n[85], $venta['vencimiento1']);
        if ($venta['vencimiento2'] != '0000-00-00')
            texto('fecha', $i18n[86], $venta['vencimiento2']);

        if ($tipoventa['tipofacturacion'] == 'electronico') {
            $conceptos = array(
                '1' => $i18n[216],
                '2' => $i18n[217],
                '3' => $i18n[218],
                );
            texto('texto', $i18n[219], $conceptos[$venta['concepto']]);
            if ($venta['concepto'] > 1) {
                texto('fecha', $i18n[239], $venta['fechainicio']);
                texto('fecha', $i18n[240], $venta['fechafin']);
            }
        }

        if ($venta['idrelacion']) {
            switch ($venta['tiporelacion']) {
                case 'servicio':
                    texto('texto', $i18n[47], $servicio['titulo'].' (N° '.$venta['idrelacion'].')', '100',
                        'servicios.php?a=ver&id='.$venta['idrelacion']);
                    break;

                case 'abono':
                    texto('texto', $i18n[48], $venta['idrelacion'], '100', 'abonos.php?a=ver&idservicio='.$venta['idrelacion']);
                    break;
            }
        }
        $generada_query = consulta_sql("SELECT ventas.idventa, ventas.numero, categorias_ventas.letra, categorias_ventas.puntodeventa, ventas.razonsocial, 'generada' AS relacion
                                        FROM ventas
                                        LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
                                        WHERE ventas.idventa IN (SELECT idventa FROM ventasxventas WHERE idrelacion = '$id')");
        $relacionada_query = consulta_sql("SELECT ventas.idventa, ventas.numero, categorias_ventas.letra, categorias_ventas.puntodeventa, ventas.razonsocial, 'relacionada' AS relacion
                                        FROM ventas
                                        LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
                                        WHERE ventas.idventa IN (SELECT idrelacion FROM ventasxventas WHERE idventa = '$id')");
        if (contar_sql($generada_query) || contar_sql($relacionada_query)) {
            $temp_generada = array();
            $temp_relacionadas = array();
            while ($temp_array = array_sql($generada_query)) {
                $temp_generada[] = array('valor' => numero_comprobante($temp_array['letra'], $temp_array['puntodeventa'], $temp_array['numero']), 'url' => 'ventas.php?a=ver&id='.$temp_array['idventa']);
            }
            while ($temp_array = array_sql($relacionada_query)) {
                $temp_relacionadas[] = array('valor' => numero_comprobante($temp_array['letra'], $temp_array['puntodeventa'], $temp_array['numero']), 'url' => 'ventas.php?a=ver&id='.$temp_array['idventa']);
            }
            if (count($temp_generada))
                enlaces($i18n[151], $temp_generada);
            if (count($temp_relacionadas))
                enlaces($i18n[139], $temp_relacionadas);
        }

        if ($_SESSION['modulo_ML'] && $venta['ML_order_id'] && contar_sql(consulta_sql("SELECT ML_estado FROM tienda WHERE ML_estado = '1' LIMIT 1"))) {

            $resultado_sql = consulta_sql("SELECT ML_order_id, ML_pack_id, ML_shipping_id FROM ventas_ml WHERE idventa = '".$id."'");

            $title_ml = '';
            $temp_enlaces = array();
            while ($temp_array = array_sql($resultado_sql)) {
                $temp_enlaces[] = array('valor' => $temp_array['ML_order_id'], 'url' => 'https://myaccount.mercadolibre.com.ar/sales/vop?orderId='.$temp_array['ML_order_id']);
                $title_ml .=  $i18n[968].': '.$temp_array['ML_pack_id'];
                $title_ml .=  '<br>'.$i18n[969].': '.$temp_array['ML_shipping_id'];
            }

            enlaces($i18n[166], $temp_enlaces, false, false, 'info', ['title' => $title_ml]);
        }
    }
    contenido_fin();

    // Moneda
    contenido_inicio($i18n[390], '50', true);
    {
        texto('titulo', $i18n[390], $venta['moneda'].' ('.$venta['simbolo'].')', 'auto');
        if ($venta['idmoneda'] != 1 && $venta['estado'] == 'abierto') {
            texto('moneda', $i18n_funciones[331], $venta['cotizacion'], 'auto');
            texto('moneda', $i18n[400], cotizacion_con_valores(1, $venta['cotizacion'], $venta['total']), 'auto');

        } else if ($venta['idmoneda'] != 1) {
            texto('moneda', $i18n_funciones[330], $venta['cotizacion_anterior'], 'auto');
            texto('moneda', $i18n[400], cotizacion_con_valores(1, $venta['cotizacion_anterior'], $venta['total']), 'auto');

        }
    }
    contenido_fin();

    salto_linea();

    // Movimientos generados
    contenido_inicio($venta['estado'] == 'abierto' ? $i18n[244] : $i18n[220], '50', true, false, false, 'id="movimientos_generados"');
    {
        $venta['esfiscal'] = $tipoventa['tipofacturacion'] == 'interno' ? false : true;
        comprobantes_movimientos($venta);
    }
    contenido_fin();

    // Listas de precios y depósitos
    contenido_inicio($i18n[302], '50', true);
    {
        texto('texto', $i18n[303], $venta['lista']);
        texto('texto', $i18n[305], $venta['deposito']);
    }
    contenido_fin();

    extras_ver();

    // Productos
    contenido_inicio($i18n[53], '100', false, false, false, ($_SESSION['mobile'] ? 'style="overflow-x: scroll;"' : ''));
    {
        comprobantes_mostrar_titulo_productosxcomprobantes($discrimina);

        $resultado_sql = consulta_sql(
            "SELECT productosxventas.*,
                tablas_unidades.nombre AS unidad,
                tablas_ivas.nombre AS iva,
                monedas.simbolo
            FROM productosxventas
                LEFT JOIN tablas_unidades ON productosxventas.idunidad = tablas_unidades.idunidad
                LEFT JOIN tablas_ivas ON productosxventas.idiva = tablas_ivas.idiva
                LEFT JOIN productos ON productosxventas.idproducto = productos.idproducto
                LEFT JOIN ventas ON productosxventas.idventa = ventas.idventa
                LEFT JOIN monedas ON ventas.idmoneda = monedas.idmoneda
            WHERE productosxventas.idventa = '".$id."'
            ORDER BY idproductoxventa");

        if (contar_sql($resultado_sql)) {
            while ($productoxventa = array_sql($resultado_sql)) {
                comprobantes_ver_productoxcomprobante($productoxventa, $discrimina);
            }

        } else {
            linea_inicio();
            {
                texto('texto', false, $i18n[192]);
            }
            linea_fin();
        }

    }
    contenido_fin();

    if ($tipoventa['discrimina'] == 'R') {
        contenido_inicio($i18n[46]);
        {
            observacion(false, $venta['observacion']);
        }
        contenido_fin();

    } else {
        contenido_inicio($i18n[46], '66');
        {
            observacion(false, $venta['observacion']);
        }
        contenido_fin();

        contenido_inicio(false, '33', false, false, false, ['id' => "totales", 'class' => "desglose"]);
        {
            if ($tipoventa['discrimina'] == 'A' || $tipoventa['discrimina'] == 'B') {
                enlaces(false, array(
                    array('tipo' => 'flotante', 'modulo' => $modulo, 'url' => 'comprobantes_ver_totales', 'id' => $id, 'valor' => $i18n[195]),
                    ));
            }

            comprobantes_mostrar_totales($venta, $tipoventa['discrimina']);
        }
        contenido_fin();
    }

}
ventana_fin();

if ($_SESSION['sistema_gratis'] && in_array($_SESSION['ad-feg'], [1, 4, 8])) {
?>
    <script charset="utf-8">
        gratis();
    </script>
<?php
}
