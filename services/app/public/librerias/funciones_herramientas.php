<?php

function herramientas_nombre_archivo($nombre, $random, $ext = ".csv") {
    return PATH_ARCHIVOS
        .$_SESSION['empresa_idempresa'].'_'
        .$random.'_'
        .$nombre
        .$ext;
}

function importar_procesar_campo($celda, $proceso, & $tabla_relacionada = false, $sep_decimal = false) {
    if($proceso['tabla'] == 'precios')
        $proceso['tipo'] = 'precio';
    if($proceso['tabla'] == 'stock')
        $proceso['tipo'] = 'stock';

    switch ($proceso['tipo']) {
        case 'entero':
            $celda = intval($celda);
            break;

        case 'stock':
            $celda = floatval(str_replace($sep_decimal, '.', $celda));
            break;

        case 'precio':
            $celda = floatval(str_replace(array($sep_decimal, '$'), array('.', ''), $celda));
            break;

        case 'flotante':
            $celda = floatval(str_replace($sep_decimal, '.', $celda));//cambio la coma si es que vine por el sep decimal seleccionado, si ya es coma y el sep es coma queda igual sino lo cambia
            break;

        case 'fecha':
            $fecha_valida = false;
            //dd-mm-yyyy
            $regla = "/^(((((0[1-9])|(1\d)|(2[0-8]))\-((0[1-9])|(1[0-2])))|((31\-((0[13578])|(1[02])))|((29|30)\-((0[1,3-9])|(1[0-2])))))\-((20[0-9][0-9])|(19[0-9][0-9])))|((29\-02\-(19|20)(([02468][048])|([13579][26]))))$/";
            if (preg_match($regla, $celda)) {
                $fecha_valida = true;
                $tmp = explode("-", $celda);
                $celda = $tmp[2]."-".$tmp[1]."-".$tmp[0];
            } else {
                //dd/mm/yyyy
                $regla = "/^(((((0[1-9])|(1\d)|(2[0-8]))\/((0[1-9])|(1[0-2])))|((31\/((0[13578])|(1[02])))|((29|30)\/((0[1,3-9])|(1[0-2])))))\/((20[0-9][0-9])|(19[0-9][0-9])))|((29\/02\/(19|20)(([02468][048])|([13579][26]))))$/";
                if (preg_match($regla, $celda)) {
                    $fecha_valida = true;
                    $tmp = explode("/", $celda);
                    $celda = $tmp[2]."-".$tmp[1]."-".$tmp[0];
                } else {
                    //yyyy/mm/dd
                    $regla = "/^(((20[0-9][0-9])|(19[0-9][0-9]))\/((((0[1-9])|(1[0-2]))\/((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))\/31)|(((0[1,3-9])|(1[0-2]))\/(29|30)))))$/";
                    if (preg_match($regla, $celda)) {
                        $fecha_valida = true;
                    } else {
                        //yyyy-mm-dd
                        $regla = "/^(((20[0-9][0-9])|(19[0-9][0-9]))\-((((0[1-9])|(1[0-2]))\-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))\-31)|(((0[1,3-9])|(1[0-2]))\-(29|30)))))$/";
                        if (preg_match($regla, $celda)) {
                            $fecha_valida = true;
                        }
                    }
                }
            }

            if (!$fecha_valida){
                $celda = "";
            } else {
                $celda = $celda;
            }
            break;

        case 'fechayhora':
            $fecha_valida = false;
            //dd-mm-yyyy
            $regla = "/^(((((0[1-9])|(1\d)|(2[0-8]))\-((0[1-9])|(1[0-2])))|((31\-((0[13578])|(1[02])))|((29|30)\-((0[1,3-9])|(1[0-2])))))\-((20[0-9][0-9])|(19[0-9][0-9])))|((29\-02\-(19|20)(([02468][048])|([13579][26])))) (0[0-9]|1[0-9]|2[0123])\:([012345][0-9])\:([012345][0-9])$/";
            if (preg_match($regla, $celda)) {
                $fecha_valida = true;
                $tmp = explode("-", $celda);
                $celda = substr($tmp[2],0,4)."-".$tmp[1]."-".$tmp[0].substr($tmp[2],4);
            } else {
                //dd/mm/yyyy
                $regla = "/^(((((0[1-9])|(1\d)|(2[0-8]))\/((0[1-9])|(1[0-2])))|((31\/((0[13578])|(1[02])))|((29|30)\/((0[1,3-9])|(1[0-2])))))\/((20[0-9][0-9])|(19[0-9][0-9])))|((29\/02\/(19|20)(([02468][048])|([13579][26])))) (0[0-9]|1[0-9]|2[0123])\:([012345][0-9])\:([012345][0-9])$/";
                if (preg_match($regla, $celda)) {
                    $fecha_valida = true;
                    $tmp = explode("/", $celda);
                    $celda = substr($tmp[2],0,4)."-".$tmp[1]."-".$tmp[0].substr($tmp[2],4);
                } else {
                    //yyyy/mm/dd
                    $regla = "/^(((20[0-9][0-9])|(19[0-9][0-9]))\/((((0[1-9])|(1[0-2]))\/((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))\/31)|(((0[1,3-9])|(1[0-2]))\/(29|30))))) (0[0-9]|1[0-9]|2[0123])\:([012345][0-9])\:([012345][0-9])$/";
                    if (preg_match($regla, $celda)) {
                        $fecha_valida = true;
                    } else {
                        //yyyy-mm-dd
                        $regla = "/^(((20[0-9][0-9])|(19[0-9][0-9]))\-((((0[1-9])|(1[0-2]))\-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))\-31)|(((0[1,3-9])|(1[0-2]))\-(29|30))))) (0[0-9]|1[0-9]|2[0123])\:([012345][0-9])\:([012345][0-9])$/";
                        if (preg_match($regla, $celda)) {
                            $fecha_valida = true;
                        }
                    }
                }
            }

            if (!$fecha_valida){
                $celda ="";
            } else {
                $celda = $celda;
            }
            break;
        case 'mail':
            $coma_pos = strpos($celda,',');
            if($coma_pos !== false){
                $celda = substr($celda, 0,$coma_pos);
            }
            if(!filter_var($celda, FILTER_VALIDATE_EMAIL)) {
                $celda = "";
            }
            break;
        case 'valores_aceptados':
            $celda_minuscula = mb_strtolower($celda);
            $celda = '';
            foreach ($proceso['valores'] as $contenido => $valores) {
                if ($celda_minuscula === $contenido || in_array($celda_minuscula, $valores)) { //=== porque no es lo mismo 0% que '' en tablas_ivas
                    if ($_SESSION['configuracion_discrimina'] && $tabla_relacionada == 'idiva' && $contenido === 0){ //parche no aplica
                        $contenido = 1;
                    }
                    $celda = $contenido;
                    break; // Salgo del foreach porque ya encontré el contenido para ese valor aceptado
                }
            }
            break;
        case 'tabla_relacionada':
            // TODO: Desarrollar, es igual a categoria_relacionada pero no se pueden agregar campos
            if($celda == ''){
                $celda = 'Sin especificar';
            }
            $celda = array_search(strtolower($celda), array_map('strtolower', $tabla_relacionada));
            break;
        case 'sucursales':
        case 'categoria_relacionada':
            if($celda == ''){
                $celda = 'Sin especificar';
            }
            if (array_search(strtolower($celda), array_map('strtolower', $tabla_relacionada)) === false){
                $array = array_keys($tabla_relacionada);
                $indice = max($array);
                $indice++;
                /*como no existe en la tabla relacionada lo agrego en el array luego chequeare que no exista y pongo el insert*/
                //$tabla_relacionada[$indice+1] = $celda;
                //$tabla_relacionada[$indice+rand(0000, 9999)] = $celda; //en +1 era siempre +1 x max($array) es siempre max($array)
                $tabla_relacionada[$indice] = $celda; //quitamos rand y agregamos $indice++;
            }
            $celda = array_search(strtolower($celda), array_map('strtolower', $tabla_relacionada));
            break;
        case 'cuit':
            $celda = str_replace("-", "", $celda);
            $celda = str_replace("/", "", $celda);
            break;
    }

    return $celda;
}

function herramientas_generar_opcion($opcion,$valor="")
{
    global $i18n_herramientas;

	switch ($opcion['tipo']) {
		case 'lista':
            $por_defecto = $opcion['por_defecto'];
            if ($valor != ""){
                $por_defecto = $valor;
            }
			selector_array($opcion['nombre'], $opcion['titulo'], $por_defecto, '40', $opcion['opciones']);
            if ($opcion['ayuda_puntual']) {
                bajo_linea();
                texto('italica', false, $opcion['ayuda_puntual'], '60');
            }
            salto_linea();
			break;

        case 'sep_decimal':
            $por_defecto = '0';
            if ($valor != ""){
                $por_defecto = $valor;
            }
            selector_array('sep_decimal', $i18n_herramientas[7], $por_defecto, '40', array(
                array('id' =>'0' , 'valor'=> $i18n_herramientas[15]),
                array('id' =>',' , 'valor'=> $i18n_herramientas[8]),
                array('id' =>'.' , 'valor'=> $i18n_herramientas[9]),
                ));
            if ($opcion['ayuda_puntual']) {
                bajo_linea();
                texto('italica', false, $opcion['ayuda_puntual'], '60');
            }
            salto_linea();
            break;
		default:
			break;
	}
}

function herramientas_obtener_valor_tabla($campo, $id, $random)
{
    $result = false;
    /*TODO ver porque le tengo que agregar el ../ para acceder desde el archivo de procesos*/
    if ($csv_tabla_relacionada = fopen(herramientas_nombre_archivo($campo.'_tabla_procesada', $random), 'r')){

        while ($linea = fgetcsv($csv_tabla_relacionada, 0, SEPARADOR_CSV)) {
            if ($linea[0] == $id){
                $result = $linea[1];
            }
        }
        fclose($csv_tabla_relacionada);
    }
    return $result;
}

function herramientas_generar_selector_clave($columnas, $valor = "")
{
    global $i18n_herramientas;

    $array = array();
    $pk = "";
    foreach ($columnas as $campo => $opcion) {
        if ($opcion["usar_como_clave"]){
            $array[] = array('id' =>$campo, 'valor'=> $opcion["nombre"]);
        }
        if ($opcion["clave_primaria"]){
            $pk = $campo;
        }
    }

    if (count($array) > 1) {
        $por_defecto = ',';
        if ($valor != ""){
            $por_defecto = $valor;
        }
        selector_array('campo_clave', $i18n_herramientas[10], $por_defecto, '40', $array);
        bajo_linea();
        texto('italica', false, $i18n_herramientas[11], '60', false, 'puntual');
        salto_linea();

    } else {
        entrada('hidden', 'campo_clave', 'campo_clave', $pk);
    }
}

function herramientas_generar_validacion_js_opciones($opciones)
{
    echo'<script>
    $( "form" ).submit(function( event ) {
      ';
    foreach ($opciones as $key => $value) {
        if (isset($value['validar']) && $value['validar'] != '' ){
            echo 'if (!'.$value['validar'].'()){
                return false;
            }';
        }
    }

    echo '});
    </script>';
}

function opciones_sucursales($opciones, $datos)
{
    $array_precios = ['precio', 'preciofinal', 'utilidad'];
    $array_stock = ['stockactual', 'stockminimo', 'stockideal'];

    if(!$opciones['idlista']){
        foreach ($datos as $key => $value) {
            if(array_search($value, $array_precios) !== false) {
                unset($datos[$key]);   //quito valores del array
            }
        }
    }
    if(!$opciones['iddeposito']){
        foreach ($datos as $key => $value) {
            if(array_search($value, $array_stock) !== false) {
                unset($datos[$key]);   //quito valores del array
            }
        }
    }

    return $datos;
}

function buscar_array_multiple($valor, $array)
{
    foreach ($array as $item)
    {
        if (!is_array($item))
        {
            if ($item == $valor)
            {
                return true;
            }
            continue;
        }
        if (in_array($valor, $item))
        {
            return true;
        } else if (buscar_array_multiple($valor, $item)){
            return true;
        }
    }
    return false;
}

function obtener_tags($id, $modulo, $ml = false)
{
    global $i18n_funciones;

    //Filtro los tags disponibles para el modulo actual
    $array_tags = array();
    $resultado_sql = consulta_sql("SELECT * FROM tablas_tags WHERE modulo = '".$modulo."'");

    if (contar_sql($resultado_sql)) {
        $sql_global = "";

        while ($tag = array_sql($resultado_sql)) {

            if ($sql_global!= '') {
                $sql_global.=" UNION ALL ";
            }

            if ($tag['tabla_fk']) {
                // Tabla relacionada
                if ($tag['tag'] == 'saldo-cliente' || $tag['tag'] == 'mp-venta-cliente'){
                    $sql_global.= "SELECT '".$tag['tag']."' AS tag,
                        ".$tag['campo_mostrar']." AS valor
                        FROM saldos
                        WHERE tiporelacion = '".$tag['tabla_fk']."' AND idrelacion =
                        (SELECT ".$tag['campo_fk']." FROM ".$modulo." WHERE ".$tag['campo']." = ".$id.")";

                } else {
                    $sql_global.= "SELECT '".$tag['tag']."' AS tag,
                        ".$tag['tabla_fk'].".".$tag['campo_mostrar']." AS valor
                        FROM ".$modulo."
                        JOIN ".$tag['tabla_fk']." ON ".$modulo.".".$tag['campo']."=".$tag['tabla_fk'].".".$tag['campo_fk']."
                        WHERE id".substr($modulo,0,-1)." = ".$id;
                }

            } else {

                if ($tag['tag'] == 'saldo' || $tag['tag'] == 'mp' || $tag['tag'] == 'mp-cliente'){
                    $sql_global.= "SELECT '".$tag['tag']."' AS tag,
                        ".$tag['campo']." AS valor
                        FROM saldos
                        WHERE tiporelacion = '".$modulo."' AND idrelacion = ".$id;
                } else {
                    $sql_global.= "SELECT '".$tag['tag']."' AS tag,
                            ".$tag['campo']." AS valor
                        FROM ".$modulo."
                        WHERE id".substr($modulo,0,-1)." = ".$id;
                }
            }
        }

        $resultado_sql = consulta_sql($sql_global);
        while ($tag = array_sql($resultado_sql)) {
            //Genero botón de pago
            $tienda = array_sql(consulta_sql("SELECT * FROM tienda WHERE idtienda = '1' LIMIT 1")); // idtienda = 1 ???????
            if (($ml) && ($tienda['MP_estado'] == 1 && $tag['tag'] == 'mp' || $tag['tag'] == 'mp-cliente' || $tag['tag'] == 'mp-venta-cliente')){
                $datos = array_sql(consulta_sql("SELECT idcliente AS iddestinatario, mail AS destinatario FROM clientes WHERE idcliente = (SELECT idcliente FROM ventas WHERE idventa = '".$id."')"));

                try {
                    $preference = crear_boton_mp(
                        $tienda['MP_client_id'], //MP_id
                        $tienda['MP_client_secret'], //MP_secret
                        abs($tag['valor']), //Saldo
                        ($tag['tag'] == 'mp' ? $i18n_funciones[167].$_SESSION['empresa_nombre'] : $i18n_funciones[168].$_SESSION['empresa_nombre']), //Nos olvidamos del título en todos lados
                        $datos['destinatario'], //email destinatario
                        $i18n_funciones[163].'|'.$_SESSION["empresa_idempresa"].'-'.$datos['iddestinatario'], //Ext ref + idempresa - idcliente
                        ($tag['tag'] == 'mp' ?
                            $i18n_funciones[167] .$_SESSION['empresa_nombre'] : $i18n_funciones[168].$_SESSION['empresa_nombre']) //Descripción
                    );
                    $tag['valor'] = '<a href="'.$preference["response"]["init_point"].'" name="MP-Checkout" class="blue-tr-l-rn-arall"> <img alt="Pagar con MercadoPago" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/btn-pago-mp.jpg"></a>';
                } catch (Exception $ex) { //ver acá
                    //texto('texto', false, $i18n[491], 'auto'); // Mensaje de que MP no responde
                    //mostrar_error($ex, true);
                }
            }
            $array_tags[$tag['tag']] = $tag['valor'];

        }

        return $array_tags;
    }

}

function lista_ids($string) {
    if (!preg_match('#^\s*[1-9][0-9]*(,\s*[1-9][0-9]*)*$#', $string)) {
        return false;
    }
    return true;
}

function enviar_notificacion($datos, $idempresa = false)
{
    //No le mando al sin especificar
    $sql = "SELECT idusuario FROM usuarios WHERE estado = 1"
        . ($datos['usuarios_especificos'] ? " AND idusuario IN (".$datos['usuarios_especificos'].")" : " AND idusuario > 0")
        . ($datos['solo_admin'] ? " AND idperfil = 1" : "");
    $usuarios = consulta_sql($sql, $idempresa);
    $sql_values = [];

    if (contar_sql($usuarios) && $idempresa) {
        while ($usuario = array_sql($usuarios)) {
            $sql_values[] = "(
                '".$usuario['idusuario']."',
                'Notificacion',
                '".$datos['mail']."',
                0,
                '".$datos['destacado']."',
                NOW()
            )";
        }

        consulta_sql("INSERT INTO mensajes (idusuario, tipo, texto, idremitente, destacado, fecha) VALUES " .implode(',', $sql_values), $idempresa);

        return true;
    }
    return false;
}

function buscar_idempresa($idcliente)
{
    return campo_sql(consulta_sql("SELECT idempresa FROM empresas WHERE idcliente = '$idcliente' LIMIT 1", 'saasargentina'));
}

function matchear_importacion($pattern, $linea) {
    preg_match($pattern, $linea, $tmp_match);
    if (!empty($tmp_match) && isset($tmp_match[1]) && $tmp_match[1]) {
        return trim(str_replace("'", "", $tmp_match[1]));
    }
}
