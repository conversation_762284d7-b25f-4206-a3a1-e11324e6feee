<?php
ventana_informe($i18n_informes[157]);
{
    contenido_inicio($i18n_informes[10], '100');
    {
        selector('idlocalidad', $i18n_informes[158], $datos['idlocalidad'], '25', 'categorias_localidades', 'nombre',
            false, false, true, false, false,
            array('' => $i18n_informes[159], '0' => $i18n_funciones['sin_especificar'])
        );
        selector('idtipocliente', $i18n_informes[160], $datos['idtipocliente'], '25', 'categorias_clientes', 'nombre',
            false, false, true, false, false,
            array('' => $i18n_informes[161], '0' => $i18n_funciones['sin_especificar'])
        );
        selector_array('estado', $i18n_informes[162], $datos['estado'], '25',
            array(
            array('id' => '', 'valor' => $i18n_informes[163]),
            array('id' => '1', 'valor' => $i18n_informes[164]),
            array('id' => '0', 'valor' => $i18n_informes[165])
        ));
        selector_array('idtipoiva', $i18n_informes[166], $i18n_informes[167], '25', // arreglo para que el primero que se muestre sea el 'Todas las condiciones de IVA
            array(
            array('id' => '', 'valor' => $i18n_informes[167]),
            array('id' => '0', 'valor' => $i18n_informes[168]),
            array('id' => '1', 'valor' => $i18n_informes[169]),
            array('id' => '2', 'valor' => $i18n_informes[170]),
            array('id' => '6', 'valor' => $i18n_informes[171]),
            array('id' => '3', 'valor' => $i18n_informes[172])
        ));
    }
    contenido_fin();

    contenido_inicio($i18n_informes[20], '50');
    {
        marcas($i18n_informes[22], '50',
            array(
            array('titulo' => $i18n_informes[181], 'valor' => $datos['mostrar_tipocliente'], 'nombre' => 'mostrar_tipocliente'),
            array('titulo' => $i18n_informes[290], 'valor' => $datos['mostrar_lista_de_precios'], 'nombre' => 'mostrar_lista_de_precios'),
            array('titulo' => $i18n_informes[24], 'valor' => $datos['mostrar_localidad'], 'nombre' => 'mostrar_localidad'),
            array('titulo' => $i18n_informes[230], 'valor' => $datos['mostrar_provincia'], 'nombre' => 'mostrar_provincia'),
            array('titulo' => $i18n_informes[179], 'valor' => $datos['mostrar_domicilio'], 'nombre' => 'mostrar_domicilio'),
            array('titulo' => $i18n_informes[25], 'valor' => $datos['mostrar_cuit'], 'nombre' => 'mostrar_cuit')
        ));
        marcas(' ', '50',
        array_merge(
            [
                ['titulo' => $i18n_informes[382], 'valor' => $datos['mostrar_usuario'], 'nombre' => 'mostrar_usuario'],
                ['titulo' => $i18n_informes[182], 'valor' => $datos['mostrar_telefonos'], 'nombre' => 'mostrar_telefonos'],
                ['titulo' => $i18n_informes[183], 'valor' => $datos['mostrar_email'], 'nombre' => 'mostrar_email'],
                ['titulo' => $i18n_informes[261], 'valor' => $datos['mostrar_datosextra'], 'nombre' => 'mostrar_datosextra']
            ],
            $_SESSION['modulo_multimoneda'] ? [['titulo' => $i18n_informes[403], 'valor' => $datos['mostrar_moneda'], 'nombre' => 'mostrar_moneda']] : [],
            [
                ['titulo' => $i18n_informes[377], 'valor' => $datos['mostrar_saldo'], 'nombre' => 'mostrar_saldo']
            ]
        ));
    }
    contenido_fin();

    contenido_inicio($i18n_informes[70], '50');
    {
        entrada('texto', 'texto_filtrar', $i18n_informes[71], $datos['texto_filtrar'], '50', '100');
        selector_array('campo_filtrar', $i18n_informes[72], $datos['campo_filtrar'], '50',
            array(
            array('id' => 'nombre', 'valor' => $i18n_informes[173]),
            array('id' => 'contacto', 'valor' => $i18n_informes[174]),
            array('id' => 'idcliente', 'valor' => $i18n_informes[123]),
            array('id' => 'telefonos', 'valor' => $i18n_informes[175])
        ));
    }
    contenido_fin();

    botones(array(array('tipo' => 'nueva', 'valor' => $i18n_informes[26], 'opciones' => 'onclick="noDescargarInforme()"'), array('tipo' => 'nueva', 'valor' => $i18n_informes[376], 'opciones' => 'onclick="descargarInforme()"')));
}
ventana_fin();
