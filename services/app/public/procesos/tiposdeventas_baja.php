<?php
$mensaje = ''; // Uso el mensaje como flag de error
if (contar_sql(consulta_sql("SELECT ML_idtipoventa FROM tienda WHERE ML_idtipoventa = '$id' LIMIT 1"))) {
    $mensaje = $i18n[401];

} elseif (contar_sql(consulta_sql("SELECT tienda_idtipoventa FROM tienda WHERE (tienda_estado = '1' OR API_estado = '1') AND tienda_idtipoventa = '$id' LIMIT 1"))) {
    $mensaje = $i18n[402];

} elseif (contar_sql(consulta_sql("SELECT idventa FROM ventas WHERE idtipoventa = '$id' LIMIT 1"))) {
    consulta_sql("UPDATE categorias_ventas SET estado='0' WHERE idtipoventa = '$id' LIMIT 1");
    $mensaje = $i18n[446];

} else {
    consulta_sql("DELETE FROM categorias_ventas WHERE idtipoventa = '$id' LIMIT 1");
}

if ($mensaje) {
    $tipoventa = array_sql(consulta_sql(
        "SELECT * FROM categorias_ventas WHERE idtipoventa = '$id' LIMIT 1"));
    linea_inicio('fila', 2, ($tipoventa['estado'] ? 'configuraciones.php?a=modventa&id='.$tipoventa['idtipoventa'] : false));
    {
        $estado = ($tipoventa['estado'] ? 'habilitado' : 'deshabilitado');
        celda('imagen', $i18n[$estado], 'imagen', $estado);
        celda('texto', $tipoventa['letra'].completar_numero($tipoventa['puntodeventa'], 5).'-'.completar_numero($tipoventa['ultimonumero'], 8), '20');
        celda('texto', $tipoventa['nombre'], 'auto');
    }
    linea_fin(array(
        array('url' => 'configuraciones.php?a=modventa&id='.$tipoventa['idtipoventa'], 'a' => 'mod', 'title' => $i18n[187]),
        array('tipo' => 'ajax', 'url' => 'tiposdeventas.php?a=baja&id='.$tipoventa['idtipoventa'], 'a' => 'baja', 'title' => $i18n[188])
    ));

    script_flotante("alerta", $mensaje, 7000);
}
