<?php
consulta_sql("DELETE FROM categorias_clientes WHERE categorias_clientes.idtipocliente = '".$id."'
    AND NOT EXISTS (SELECT * FROM clientes WHERE clientes.idtipocliente = categorias_clientes.idtipocliente) LIMIT 1");

if (!afectado_sql()) {
    script_flotante("alerta", $i18n[295], 7000);
    $tipocliente = array_sql(consulta_sql("SELECT idtipocliente, categorias_clientes.nombre, cuentacorriente, maxcuentacorriente, pagacosto, descuento, condicion, listas.idlista, listas.nombre AS lista
        FROM categorias_clientes
        LEFT JOIN listas ON categorias_clientes.idlista = listas.idlista
        WHERE idtipocliente = '".$id."'
        LIMIT 1"));

    linea_inicio('fila', 2, '', 'id="linea_'.$id.'"');
    {
        celda('texto', $tipocliente['nombre'], '20');
        celda('texto', $i18n[$tipocliente['condicion']], '22');
        marca('cuentacorriente', $tipocliente['cuentacorriente'], '7', 'disabled="disabled"');
        celda('moneda', $tipocliente['maxcuentacorriente'], '15');
        marca('pagacosto', $tipocliente['pagacosto'], '8', 'disabled="disabled"');
        celda('descuento', $tipocliente['descuento'], '15');
        celda('texto', $tipocliente['lista']);
    }
    linea_fin(array(array('tipo' => 'ajax', 'url' => 'categorias_clientes.php?a=mod&id='.$id, 'a' => 'mod', 'title' => $i18n[62], 'opciones' => 'id="mod_'.$id.'"'), array('tipo' => 'ajax', 'url' => 'categorias_clientes.php?a=baja&id='.$id, 'a' => 'baja', 'title' => $i18n[63])), 'id="botones_'.$id.'"');
    linea_inicio('fila', 2, '', 'id="entrada_'.$id.'" style="display: none;"');
    {
        entrada('texto', 'nombre', '', $tipocliente['nombre'], '20', '60');
        selector_array('condicion', '', $tipocliente['condicion'], '22',
                array(
                array('id' => 'contado', 'valor' => $i18n['contado']),
                array('id' => 'cuentacorriente', 'valor' => $i18n['cuentacorriente']),
                array('id' => 'debito', 'valor' => $i18n['debito']),
                array('id' => 'credito', 'valor' => $i18n['credito']),
                array('id' => 'cheque', 'valor' => $i18n['cheque']),
                array('id' => 'ticket', 'valor' => $i18n['ticket']),
                array('id' => 'otra', 'valor' => $i18n['otra'])
            ));
        marca('cuentacorriente', $tipocliente['cuentacorriente'], '7');
        entrada('moneda', 'maxcuentacorriente', '', $tipocliente['maxcuentacorriente'], '15', '9');
        marca('pagacosto', $tipocliente['pagacosto'], '8');
        entrada('descuento', 'descuento', '', $tipocliente['descuento'], '15', '');
        selector('idlista', '', $tipocliente['idlista'], '12', 'listas', 'idlista', false, false);
    }
    linea_fin(array(array('tipo' => 'ajax', 'url' => 'categorias_clientes.php?a=mod&id='.$id, 'a' => 'ok', 'value' => $i18n[65]), array('tipo' => 'ajax', 'url' => 'categorias_clientes.php?a=baja&id='.$id, 'a' => 'no', 'value' => $i18n[66])));

    echo '
<script type="text/javascript" charset="utf-8">
    var lineas = $("#linea_'.$id.' .linea_contenido, #entrada_'.$id.' .linea_contenido");
    lineas.addClass("linea_contenido_2_100");
    var ancho = lineas.first().width()-0;
    lineas.width(ancho);
</script>';
}
