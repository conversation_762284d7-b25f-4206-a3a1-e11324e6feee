<?php
$temp_array = explode('_', $boton);
$boton = $temp_array[0];
$cantidad = $temp_array[1];

$temp_array = explode('_', $id_boton);
$tipo = $temp_array[0];
$saldo = $temp_array[1];

if ($tipo == 'cheque' && $_SESSION['control_cheques_ordenar'] == 'fechacobro DESC')
    $orden = 'IF(fechacobro IS NULL, fechaconciliacion , fechacobro) DESC';
else
    $orden = 'fecha DESC';

$orden .= ', idmovimientoxcaja ASC';

$sql = "SELECT movimientosxcajas.*, cheques.fechacobro, compraspagos.idnumerocomprapago AS idnumero,
            categorias_conceptos.padres, categorias_conceptos.idconceptopadre, categorias_conceptos.nombre AS concepto,
            categorias_cajas.tipo,
            monedas.simbolo
        FROM movimientosxcajas
            LEFT JOIN cajas ON movimientosxcajas.idcaja = cajas.idcaja
            LEFT JOIN categorias_cajas ON cajas.idtipocaja = categorias_cajas.idtipocaja
            LEFT JOIN categorias_conceptos ON movimientosxcajas.idconcepto = categorias_conceptos.idconcepto
            LEFT JOIN compraspagos ON movimientosxcajas.idrelacion = compraspagos.idcomprapago
            LEFT JOIN cheques ON compraspagos.idrelacion = cheques.idcheque AND compraspagos.tiporelacion = 'cheque'
            LEFT JOIN monedas ON cajas.idmoneda = monedas.idmoneda
        WHERE movimientosxcajas.idcaja = '$id'
            AND movimientosxcajas.tiporelacion = 'proveedorpago'

        UNION

        SELECT movimientosxcajas.*, cheques.fechacobro, ventaspagos.idnumeroventapago AS idnumero,
            categorias_conceptos.padres, categorias_conceptos.idconceptopadre, categorias_conceptos.nombre AS concepto,
            categorias_cajas.tipo,
            monedas.simbolo
        FROM movimientosxcajas
            LEFT JOIN cajas ON movimientosxcajas.idcaja = cajas.idcaja
            LEFT JOIN categorias_cajas ON cajas.idtipocaja = categorias_cajas.idtipocaja
            LEFT JOIN categorias_conceptos ON movimientosxcajas.idconcepto = categorias_conceptos.idconcepto
            LEFT JOIN ventaspagos ON movimientosxcajas.idrelacion = ventaspagos.idventapago
            LEFT JOIN cheques ON ventaspagos.idrelacion = cheques.idcheque AND ventaspagos.tiporelacion = 'cheque'
            LEFT JOIN monedas ON cajas.idmoneda = monedas.idmoneda
        WHERE movimientosxcajas.idcaja = '$id'
            AND movimientosxcajas.tiporelacion = 'clientepago'

        UNION

        SELECT movimientosxcajas.*, COALESCE(null) AS fechacobro, COALESCE(0) AS idnumero, categorias_conceptos.padres,
            categorias_conceptos.idconceptopadre, categorias_conceptos.nombre AS concepto,
            categorias_cajas.tipo,
            monedas.simbolo
        FROM movimientosxcajas
            LEFT JOIN cajas ON movimientosxcajas.idcaja = cajas.idcaja
            LEFT JOIN categorias_cajas ON cajas.idtipocaja = categorias_cajas.idtipocaja
            LEFT JOIN categorias_conceptos ON movimientosxcajas.idconcepto = categorias_conceptos.idconcepto
            LEFT JOIN monedas ON cajas.idmoneda = monedas.idmoneda
        WHERE movimientosxcajas.idcaja = '$id'
            AND movimientosxcajas.tiporelacion != 'clientepago' AND movimientosxcajas.tiporelacion != 'proveedorpago'

        ORDER BY ".$orden."";

if ($boton == 'todos') {
    $sql .= " LIMIT " . $cantidad . ", 1000";
} else {
    $sql .= " LIMIT " . $cantidad . ", " . $_SESSION['control_agrandar'];
}
$resultado_sql = consulta_sql($sql);

$i = 0;
while ($ordenar[$i] = array_sql($resultado_sql)) {
    $saldo -= $ordenar[$i]['total'];
    $i++;
}
if ($i == $_SESSION['control_agrandar']) {
    agrandar(array(array('valor' => $i18n[63], 'url' => 'cajas_agrandar', 'cantidad' => ($cantidad + $_SESSION['control_agrandar']), 'id_boton' => $tipo.'_'.$saldo, 'arriba' => true), array('valor' => $i18n[64], 'url' => 'cajas_agrandar', 'cantidad' => ($cantidad + $_SESSION['control_agrandar']), 'opciones' => 'todos', 'id_boton' => $tipo.'_'.$saldo, 'arriba' => true)));
}
while ($i > 0) {
    $i--;
    $movimiento = $ordenar[$i];
    // COPIADO DESDE CAJAS_VER

    $movimiento['tipo'] = $ordenar[$i]['tipo'];
    linea_inicio('fila', ($movimiento['tipo'] == 'cheque' ? 1 : 0), '', 'id="linea_' . $movimiento['idmovimientoxcaja'] . '"');
    {
        // COPIADO DESDE CAJAS_VER
        if ($movimiento['total'] >= 0) {
            $saldo += $movimiento['total'];
            $movimiento['ingreso'] = $movimiento['total'];
            $movimiento['egreso'] = '';
        } else {
            $saldo += $movimiento['total'];
            $movimiento['egreso'] = -$movimiento['total'];
            $movimiento['ingreso'] = '';
        }

        switch ($movimiento['tiporelacion']) {
            case 'clientepago':
                $link = 'ventas.php?a=verpago&id=' . $movimiento['idrelacion'];
                if (strpos($movimiento['detalle'], $i18n[124]) !== false) {
                    $movimiento['detalle'] = $i18n[124] . 'RP'.completar_numero($movimiento['idnumero'], 8);
                }
                $numero_extra = campo_sql(consulta_sql(
                    "SELECT numero
                    FROM ventasxclientes
                    WHERE idtipoventa = 0
                        AND ventasxclientes.id = " . $movimiento['idrelacion'] . "
                    LIMIT 1"    ), 0);
                $nombre_extra = campo_sql(consulta_sql(
                    "SELECT nombre
                    FROM clientes
                    WHERE clientes.idcliente =
                        (SELECT idcliente FROM ventaspagos WHERE ventaspagos.idventapago = " . $movimiento['idrelacion'] . " LIMIT 1)
                    LIMIT 1"    ), 0);
                $detalle_extra = " (" . $nombre_extra . " | " . ($numero_extra ? $numero_extra : $i18n[75]) . ")";
                break;

            case 'proveedorpago':
                $link = 'compras.php?a=verpago&id=' . $movimiento['idrelacion'];
                if (strpos($movimiento['detalle'], $i18n[125]) !== false) {
                    $movimiento['detalle'] = $i18n[125] . 'OP'.completar_numero($movimiento['idnumero'], 8);
                }
                $numero_extra = campo_sql(consulta_sql(
                    "SELECT numero
                    FROM comprasxproveedores
                    WHERE idtipocompra = 0
                        AND comprasxproveedores.id = " . $movimiento['idrelacion'] . "
                    LIMIT 1"    ), 0);
                $nombre_extra = campo_sql(consulta_sql(
                    "SELECT nombre
                    FROM proveedores
                    WHERE proveedores.idproveedor =
                        (SELECT idproveedor FROM compraspagos WHERE compraspagos.idcomprapago = " . $movimiento['idrelacion'] . " LIMIT 1)
                    LIMIT 1"    ), 0);
                $detalle_extra = " (" . $nombre_extra . " | " . ($numero_extra ? $numero_extra : $i18n[75]) . ")";
                break;

            default:
                $link = false;
                $detalle_extra = '';
                break;
        }

        if ($movimiento['padres']) {
            $movimiento['concepto'] = nombre_con_padres('categorias_conceptos', $movimiento['idconcepto']);
        }

        if ($movimiento['tipo'] == 'cheque') {
            if ($movimiento['tiporelacion'] == 'transferencia') {
                celda('largo', '', '3');    //Nada!
            } else {
                marca('conciliacion_actual' . $movimiento['idmovimientoxcaja'], $movimiento['conciliacion'], 'imagen', 'disabled="disabled"');
            }
            if ($movimiento['tiporelacion'] != 'transferencia' && $_SESSION['control_cheques_ordenar'] == 'fechacobro DESC')
                $fecha = $movimiento['fechacobro'];
            elseif ($movimiento['tiporelacion'] == 'transferencia' && $_SESSION['control_cheques_ordenar'] == 'fechacobro DESC')
                $fecha = $movimiento['fechaconciliacion'];
            else
                $fecha = $movimiento['fecha'];
            celda('fecha', $fecha, '12');
        } else if ($movimiento['tipo'] == 'banco' || $movimiento['tipo'] == 'retencion'){
            marca('conciliacion_'.$movimiento['idmovimientoxcaja'], $movimiento['conciliacion'], 'imagen', 'disabled="disabled"');
            celda('fechayhora', $movimiento['fecha'], '12');
        } else {
            celda('fechayhora', $movimiento['fecha'], '12');
        }
        celda('largo', $movimiento['concepto'], '20');
        if ($detalle_extra) {
            celda('largo', $movimiento['detalle'], '19', false, $link);
            celda('largo', $detalle_extra, '14');
        } else {
            celda('largo', $movimiento['detalle'] . $detalle_extra, '33', false, $link);
        }
        celda('moneda', $movimiento['ingreso'], '10', false, false, false, false, $movimiento['simbolo']);
        celda('moneda', $movimiento['egreso'], '10', false, false, false, false, $movimiento['simbolo']);
        celda('moneda', $saldo, false, false, false, false, false, $movimiento['simbolo']);
        // COPIADO DESDE CAJAS_VER
    }
    linea_fin();
}
