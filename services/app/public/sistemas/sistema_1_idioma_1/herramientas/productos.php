<?php
$idmodulo = 'idproducto';
$clave = 'codigo';
$nombre_amigable = "Productos";
$idlista = recibir_variable('idlista');
$iddeposito = recibir_variable('iddeposito');
$columnas = array(
    'estado' => array('sql' => "if (productos.estado, 'Habilitado', 'Deshabilitado') AS estado", 'nombre' => 'Estado'),
    'estadoventa' => array('sql' => "if (productos.estadoventa, 'SI', 'NO') AS estadoventa", 'nombre' => 'Disponible para la venta'),
    'estadocompra' => array('sql' => "if (productos.estadocompra, 'SI', 'NO') AS estadocompra", 'nombre' => 'Disponible para la compra'),
    'estadocombo' => array('sql' => "if (productos.estadocombo, 'SI', 'NO') AS estadocombo", 'nombre' => 'Disponible para integrar otros productos'),
    'combo' => array('sql' => "if (productos.combo, 'SI', 'NO') AS combo", 'nombre' => 'Compuesto por otros productos'),
    'idrubro' => array('sql' => "categorias_rubros.nombre AS rubro", 'nombre' => 'Rubro'),
    'nombre' => array('sql' => "productos.nombre", 'nombre' => 'Nombre'),
    'codigo' => array('sql' => "productos.codigo", 'nombre' => 'Código', 'usar_como_clave' => true),
    'codigoproveedor' => array('sql' => "productos.codigoproveedor", 'nombre' => 'Código de proveedor', 'usar_como_clave' => true),
    'idproducto' => array('sql' => "productos.idproducto", 'nombre' => 'Nº de producto (Sólo uso avanzado)', 'ayuda'=> 'el identificador del producto', 'ayuda_puntual' => '','clave_primaria'=> true, 'usar_como_clave' => true),

    'stockactual' => array('sql' => "REPLACE(stock.stockactual, '.',',') AS stockactual", 'nombre' => 'Stock actual'),
    'stockideal' => array('sql' => "REPLACE(stock.stockideal, '.',',') AS stockideal", 'nombre' => 'Stock ideal'),
    'stockminimo' => array('sql' => "REPLACE(stock.stockminimo, '.',',') AS stockminimo", 'nombre' => 'Stock mínimo'),

    'idunidad' => array('sql' => "tablas_unidades.nombre AS unidad", 'nombre' => 'Unidad'),
    'idiva' => array('sql' => "tablas_ivas.nombre AS iva", 'nombre' => 'Alicuota de IVA'),
    'idproveedor' => array('sql' => "proveedores.nombre AS proveedor", 'nombre' => 'Proveedor'),

    'costo' => array('sql' => "REPLACE(productos.costo, '.',',') AS costo", 'nombre' => 'Costo ($)'),
    'utilidad' => array('sql' => "REPLACE(precios.utilidad, '.',',') AS utilidad", 'nombre' => 'Utilidad (%)'),
    'precio' => array('sql' => "REPLACE(precios.precio, '.',',') AS precio", 'nombre' => 'Precio ($)'),
    'preciofinal' => array('sql' => "REPLACE(precios.preciofinal, '.',',') AS preciofinal", 'nombre' => 'Precio Final ($)'),

    'controlarstock' => array('sql' => "if (productos.controlarstock, 'SI', 'NO') AS controlarstock", 'nombre' => 'Controla stock'),
    'stocknegativo' => array('sql' => "if (productos.stocknegativo, 'SI', 'NO') AS stocknegativo", 'nombre' => 'Stock negativo'),
    'mostrartienda' => array('sql' => "if (productos.mostrartienda, 'SI', 'NO') AS mostrartienda", 'nombre' => 'Mostrar en tienda'),

    'ML_item_id' => array('sql' => "productos.ML_item_id", 'nombre' => 'Nº de publicación en MercadoLibre'),
    'ML_item_id2' => array('sql' => "productos.ML_item_id2", 'nombre' => 'Nº de publicación adicional en MercadoLibre'),

    'observacion' => array('sql' => "productos.observacion", 'nombre' => 'Descripción'),
    'obstienda' => array('sql' => "productos.obstienda", 'nombre' => 'Descripción para la tienda'),
    'obsinterna' => array('sql' => "productos.obsinterna", 'nombre' => 'Observaciones Internas'),
    'sku' => array('sql' => "productos.sku", 'nombre' => 'Código universal de producto (UPC)'),
);
$final_sql = "
    FROM productos
        LEFT JOIN categorias_rubros ON productos.idrubro = categorias_rubros.idrubro
        LEFT JOIN tablas_unidades ON productos.idunidad = tablas_unidades.idunidad
        LEFT JOIN tablas_ivas ON productos.idiva = tablas_ivas.idiva
        LEFT JOIN proveedores ON productos.idproveedor = proveedores.idproveedor
        LEFT JOIN precios ON productos.idproducto = precios.idproducto AND precios.idlista = '".$idlista."'
        LEFT JOIN stock ON productos.idproducto = stock.idproducto AND stock.iddeposito = '".$iddeposito."'
    ORDER BY idproducto";

// Opciones para algorítmos específicos del módulo
$resultado_sql = consulta_sql("SELECT idlista, nombre AS lista FROM listas");
$opciones_listas = array();
$opciones_listas[] = array('id' => 0, 'valor' => 'No importar precios');
while ($temp_array = array_sql($resultado_sql)) {
    $opciones_listas[] = array('id' => $temp_array['idlista'], 'valor' => $temp_array['lista']);
}

$resultado_sql = consulta_sql("SELECT iddeposito, nombre AS deposito FROM depositos");
$opciones_depositos = array();
$opciones_depositos[] = array('id' => 0, 'valor' => 'No importar stock');
while ($temp_array = array_sql($resultado_sql)) {
    $opciones_depositos[] = array('id' => $temp_array['iddeposito'], 'valor' => $temp_array['deposito']);
}

$opciones = array(
    array('nombre'=>'sep_decimal', 'tipo'=>'sep_decimal', 'solapa' => 'todas'/*, 'validar'=>'seleccionar_sep_decimal'*/,
        'ayuda_puntual' => 'Indique el caracter utilizado para separar enteros de decimales en las columnas de importes.'),/*es una opcion general con esto la habilito*/
    array('nombre' => 'idlista', 'tipo' => 'lista', 'titulo' => "Lista de precios", "por_defecto" => 1,
        'ayuda_puntual' => '',
        'solapa' => 'actualizar',
        'opciones' => $opciones_listas,
    ),
    array('nombre' => 'iddeposito', 'tipo' => 'lista', 'titulo' => "Depósitos", "por_defecto" => 0,
        'ayuda_puntual' => '',
        'solapa' => 'actualizar',
        'opciones' => $opciones_depositos,
    )
    /*array('nombre' => 'modo_ajuste', 'tipo' => 'lista', 'titulo' => "Modo de ajuste de importes", "por_defecto" => "valores_existentes",
        'ayuda_puntual' => 'Esta opción define si se realizan cálculos en los importes automáticamente.',
        'solapa' => 'actualizar',
        'opciones' => array(
            array('id' =>'valores_existentes' , 'valor'=> 'No ajustar, utilizar los valores del archivo'),
            array('id' => '', 'valor'=>'No importar costo, precio, ni utilidad'),
            array('id' =>'costo_precio' , 'valor'=> 'Utilizando el costo y el precio, calcular la utilidad'),
            array('id' =>'costo_utilidad' , 'valor'=> 'Utilizando el costo y la utilidad, calcular el precio'),
            array('id' =>'precio_utilidad' , 'valor'=> 'Utilizando el precio y la utilidad, calcular el costo'),
            array('id' => 'calcular_faltante', 'valor'=>'Calcular el valor faltante'),
        )
    )*/
);

$funcion_procesa_opciones = 'procesar_costos';

// Los procesos son las conversiones especiales que hay que hacerle a ciertos campos
$procesos = array(
    'idrubro' => array('tipo' => 'categoria_relacionada', 'tabla' => 'categorias_rubros', 'nombre_amigable'=>'Rubros'),
    'idproveedor' => array('tipo' => 'categoria_relacionada', 'tabla' => 'proveedores', 'nombre_amigable'=>'Proveedores'),
    'estado' => array('tipo' => 'valores_aceptados',
        'valores' => array(
            '1' => array(1, 'habilitado', 'si'),
            '0' => array('no habilitado', 'no'),
        ),
    ),
    'estadoventa' => array('tipo' => 'valores_aceptados',
        'valores' => array(
            '1' => array(1, 'SI', 'si', 'true', 'habilitado'),
            '0' => array('NO', 'no', 'false', 'deshabilitado'),
        ),
    ),
    'estadocompra' => array('tipo' => 'valores_aceptados',
        'valores' => array(
            '1' => array(1, 'SI', 'si', 'true', 'habilitado'),
            '0' => array('NO', 'no', 'false', 'deshabilitado'),
        ),
    ),
    'estadocombo' => array('tipo' => 'valores_aceptados',
        'valores' => array(
            '1' => array(1, 'SI', 'si', 'true', 'habilitado'),
            '0' => array('NO', 'no', 'false', 'deshabilitado'),
        ),
    ),
    'combo' => array('tipo' => 'valores_aceptados',
        'valores' => array(
            '1' => array(1, 'SI', 'si', 'true', 'habilitado'),
            '0' => array('NO', 'no', 'false', 'deshabilitado'),
        ),
    ),
    'stockactual' => array('tipo' => 'flotante'),
    'stockideal' => array('tipo' => 'flotante'),
    'stockminimo' => array('tipo' => 'flotante'),
    'controlarstock' => array('tipo' => 'valores_aceptados',
        'valores' => array(
            '1' => array(1, 'SI', 'si', 'true', 'habilitado'),
            '0' => array('NO', 'no', 'false', 'deshabilitado')),
        ),
    'stocknegativo' => array('tipo' => 'valores_aceptados',
        'valores' => array(
            '1' => array(1, 'SI', 'si', 'true', 'habilitado'),
            '0' => array('NO', 'no', 'false', 'deshabilitado')),
        ),
    'mostrartienda' => array('tipo' => 'valores_aceptados',
        'valores' => array(
            '1' => array(1, 'SI', 'si', 'true', 'habilitado'),
            '0' => array('NO', 'no', 'false', 'deshabilitado')),
        ),
    'idiva' => array('tipo' => 'valores_aceptados',
        'valores' => array(
            '0' => array('no aplica', 'No Aplica', ''),
            '1' => array('no gravado', 'No gravado', 'nogravado', 'NG'),
            '2' => array('exento', 'Ex', 'Exento'),
            '3' => array('0%', '0', '% 0','0%'),
            '4' => array('10.50', '10.5', '10,5', '10.50', '10,5%', '10,50%', '10.50 %', '10,50 %', '0.105'),
            '5' => array('21,00%', '21,00', '21', '21.00', '21.0%', '21%', '21.00%', '21.00 %', '21,00 %', '21 %', '0.21'),
            '6' => array('27.00 %', '27.00%', '27', '27.00', '27,00', '27,00%', '27%', '27,00 %', '27 %', '0.27'),
            '8' => array('5 %', '5', '5.00', '5,00', '5.00%', '5,00%', '5,00 %', '5.0%', '5%', '0.05'),
            '9' => array('2,5%', '2.5', '2,5', '2.50', '2,50', '2.50%', '2.50 %', '2,50 %', '2,50 %', '2,5%', '0.025'),
        ),
    ),
    'idunidad' => array('tipo' => 'tabla_relacionada', 'tabla' => 'tablas_unidades', 'nombre_amigable'=>'Unidad'),
    'precio' => array('tipo' => 'sucursales', 'tabla' => 'precios', 'nombre_amigable'=>'Precio'),
    'preciofinal' => array('tipo' => 'sucursales', 'tabla' => 'precios', 'nombre_amigable'=>'Precio final'),
    'utilidad' => array('tipo' => 'sucursales', 'tabla' => 'precios', 'nombre_amigable'=>'Utilidad'),
    'stockactual' => array('tipo' => 'sucursales', 'tabla' => 'stock', 'nombre_amigable'=>'Stock actual'),
    'stockideal' => array('tipo' => 'sucursales', 'tabla' => 'stock', 'nombre_amigable'=>'Stock ideal'),
    'stockminimo' => array('tipo' => 'sucursales', 'tabla' => 'stock', 'nombre_amigable'=>'Stock mínimo'),
);
