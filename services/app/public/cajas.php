<?php
//Especifico el nombre del módulo en plural y singular para ser utilizado en varias partes de inclusiones
$modulo = 'cajas';
$modulo_singular = 'caja';

//Ejecuto el archivo que se encarga de revisar que todo esté ok e incluye todas las librerías.
require 'cargadores/iniciar.php';

//Ejecuto el switch principal con el comando enviado en $a
switch ($a) {
    default:
    case 'listar':
        html_inicio();
        html_encabezado();
        html_menu();
        html_cuerpo();
        $ventana = 'cajas_listar';
        marco_inicio();
        include('ventanas/'.$ventana.'.php');
        marco_fin();
        html_cuerpo_fin();
        html_fin();
        logs();
        break;

    case 'ver':
        if (!$id || !contar_sql(consulta_sql("SELECT idcaja FROM cajas WHERE idcaja='".$id."' LIMIT 1")))
            ir_inicio($i18n_funciones['url_incorrecto'], $modulo, $a, $id);
        elseif ($_SESSION['perfil_idperfil'] != '1' && !contar_sql(consulta_sql("SELECT idtipocaja FROM categorias_cajas WHERE compartida='1' AND idtipocaja=(SELECT idtipocaja FROM cajas WHERE idcaja='".$id."')")) && !contar_sql(consulta_sql("SELECT idtipocaja FROM perfilesxcajas WHERE idperfil='".$_SESSION['perfil_idperfil']."' AND ver='1' AND idtipocaja=(SELECT idtipocaja FROM cajas WHERE idcaja='".$id."')")))
            ir_atras('No tiene permiso para ver esta caja', $modulo, $a, $id);
        else {
            html_inicio();
            html_encabezado();
            html_menu();
            html_cuerpo();
            $ventana = 'cajas_ver';
            marco_inicio();
            include('ventanas/'.$ventana.'.php');
            marco_fin();
            html_cuerpo_fin();
            html_fin();
            logs();
        }
        break;

    case 'mod':
        if (!$id || !contar_sql(consulta_sql("SELECT idcaja FROM cajas WHERE idcaja='".$id."' LIMIT 1")))
            ir_inicio($i18n_funciones['url_incorrecto'], $modulo, $a, $id);
        elseif ($_SESSION['perfil_idperfil'] != '1' && !contar_sql(consulta_sql("SELECT idtipocaja FROM categorias_cajas WHERE compartida='1' AND idtipocaja=(SELECT idtipocaja FROM cajas WHERE idcaja='".$id."')")) && !contar_sql(consulta_sql("SELECT idtipocaja FROM perfilesxcajas WHERE idperfil='".$_SESSION['perfil_idperfil']."' AND ver='1' AND idtipocaja=(SELECT idtipocaja FROM cajas WHERE idcaja='".$id."')")))
            ir_atras('No tiene permiso para modificar esta caja', $modulo, $a, $id);
        else {

            html_inicio();
            html_encabezado();
            html_menu();
            html_cuerpo();
            $ventana = 'cajas_mod';
            marco_inicio();
            include('ventanas/'.$ventana.'.php');
            mensajes_efimeros();
            include('ventanas/'.$ventana.'_vista.php');
            marco_fin();
            html_cuerpo_fin();
            html_fin(
                //array('comprobantes') // Archivos JS para cargar
                );
            logs();
        }
        break;

    case 'exportar':
        if (!$_SESSION['perfil_cajas_herramientas'])
            ir_atras('No tiene permiso para utilizar las herramientas del módulo '.$modulo, $modulo, $a, $id);
        else
            $encoding = recibir_variable('encoding', true);
            if($encoding){
                header('Location: '.URL_SCRIPTS.'/?script=exportar&modulo='.$modulo.'&encoding='.$encoding);
            } else {
                html_inicio();
                html_encabezado();
                html_menu();
                html_cuerpo();
                $ventana = 'exportar_csv';
                marco_inicio();
                include('ventanas/'.$ventana.'.php');
                marco_fin();
                html_cuerpo_fin();
                html_fin();
            }
        break;
}
