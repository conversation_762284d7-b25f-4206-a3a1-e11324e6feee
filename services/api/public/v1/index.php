<?php
require __DIR__.'/../../../acc/acc.php';
require __DIR__.'/../../vendor/autoload.php';
require __DIR__.'/../../funciones_api.php';

// Esto está deprecado, hay que pasar al autoload
function __autoload($classname) {
    $filename = $classname . '.php';
    if (is_readable($filename)) {
        require_once $filename;
    }
}

spl_autoload_register('__autoload');

// PARCHE: Mando un montón de headers para que funcione el cross-domain, pero hay que limpiar y dejar solo los que tienen que ir teniendo en cuenta la seguridad
if (preg_match("/MSIE/i", $_SERVER['HTTP_USER_AGENT'])) {
    header('P3P: CP="IDC DSP COR ADM DEVi TAIi PSA PSD IVAi IVDi CONi HIS OUR IND CNT"');
}
header('Content-type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
//header('Access-Control-Allow-Headers', 'x-requested-with, X-CSRFToken');
header('Access-Control-Max-Age: 1000');

$metodo = $_SERVER['REQUEST_METHOD'];
$body = file_get_contents('php://input');
$url = filter_input(INPUT_GET, 'url', FILTER_SANITIZE_URL);
$iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);
if (!$iue && isset($_GET['IUE']))
    $iue = filter_input(INPUT_GET, 'IUE', FILTER_SANITIZE_URL);

if ($iue == 'HU7jaq4YHEe39T3zPUrn')
    exit('IUE Bloqueado por exceso de consumo');

$recursos = array();
$temp_array = array_filter(explode('/', $url));

for ($i = 0, $cantidad = count($temp_array); $i < $cantidad; $i++) {
    if (!in_array($temp_array[$i], ['api', '$1', 'v0.1', 'v0.2', 'ml', 'v1'])) {
        if (strpos($temp_array[$i], '=') === false) {
            $recursos[] = $temp_array[$i];
        } else {
            $temp_array = explode('=', $temp_array[$i]);
            $recursos[$temp_array[0]] = $temp_array[1];
        }
    }
}

$client = new SaasClient();
$client->setCallback(filter_input(INPUT_GET, 'callback', FILTER_SANITIZE_URL));

// PARCHE: otro parche horrible porque no puedo hacer que me tome la misma session_id() así que lo fuerzo enviándolo
if ($_GET['S']) {
    session_id($_GET['S']);
} elseif ($_POST['S']) {
    session_id($_POST['S']);
} elseif ($metodo == "PUT" || $metodo == "DELETE") {
    parse_str($body, $parametros);
    if ($parametros['S']) {
        session_id($parametros['S']);
    }
}
session_start();

// LIMITACIÓN PARA BLOQUEAR CONSUTLAS EN PARALELAS AL MISMO RECURSO'_'.$recursos[0].
$consultando_api = PATH_ARCHIVOS.$iue.'_'.$recursos[0].'.consultando_api';
$recursos_consultas_paralelas = ['rubros'];
if (file_exists($consultando_api)
    && !in_array($recursos[0], $recursos_consultas_paralelas)) {
    file_put_contents(PATH_LOGS.'api-v1/' . date("Y-m-d") . '.log', "\r\n" . "\r\n" . date("Y-m-d H:i:s")
        . ' - No se pueden ejecutar consultas en paralelo al mismo recurso: '
        . $iue.'_'.$recursos[0], FILE_APPEND);
    http_response_code(429);
    exit(json_encode(array('error' => 'No se pueden ejecutar consultas en paralelo al mismo recurso')));

} else {
    touch($consultando_api);
}

// PARCHE: Mando los datos a un log para saber que pasa
file_put_contents(PATH_LOGS.'api-v1/' . date("Y-m-d") . '.log', "\r\n" . "\r\n" . date("Y-m-d H:i:s") . ' - ' . $_SERVER['SERVER_NAME'], FILE_APPEND);
file_put_contents(PATH_LOGS.'api-v1/' . date("Y-m-d") . '.log', "\r\n" . 'REQUEST: ' . $metodo . " " . $url, FILE_APPEND);
file_put_contents(PATH_LOGS.'api-v1/' . date("Y-m-d") . '.log', "\r\n" . 'BODY: ' . $body, FILE_APPEND);
file_put_contents(PATH_LOGS.'api-v1/' . date("Y-m-d") . '.log', "\r\n" . 'GET: ' . json_encode($_GET), FILE_APPEND);
file_put_contents(PATH_LOGS.'api-v1/' . date("Y-m-d") . '.log', "\r\n" . 'POST: ' . json_encode($_POST), FILE_APPEND);
file_put_contents(PATH_LOGS.'api-v1/' . date("Y-m-d") . '.log', "\r\n" . 'PARAMETROS: ' . json_encode($parametros), FILE_APPEND);
file_put_contents(PATH_LOGS.'api-v1/' . date("Y-m-d") . '.log', "\r\n" . 'SESSION_ID: ' . session_id(), FILE_APPEND);


switch ($recursos[0]) {
    default:
        $client->rechazarRecurso(400, 'Recurso inválido');
        break;

    case "notificaciones":
        switch ($recursos[1]) {
            default:
                $client->rechazarRecurso(400, 'Tipo de notificación inválidas');
                break;
        }
        break;

    case 'productos':
        if ($metodo == 'GET') {
            if (!$recursos[1]) {
                $datos = array(
                    'iue' => $iue,
                    'busqueda' => filter_input(INPUT_GET, 'busqueda', FILTER_SANITIZE_STRING),
                    'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array("options" => array("default" => 0, "min_range" => 0, "max_range" => 1000000))),
                    'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array("options" => array("default" => 10, "min_range" => 1, "max_range" => 100)))),
                    'modificados' => filter_input(INPUT_GET, 'modificados', FILTER_VALIDATE_INT, array("options" => array("default" => 0, "min_range" => 1, "max_range" => 1000))),
                    'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),
                    'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),
                    'idrubro' => filter_input(INPUT_GET, 'idrubro', FILTER_SANITIZE_NUMBER_INT),
                    'ML_item_id' => filter_input(INPUT_GET, 'ML_item_id', FILTER_SANITIZE_NUMBER_INT),
                    'mostrarcosto' => filter_input(INPUT_GET, 'mostrarcosto', FILTER_SANITIZE_NUMBER_INT),
                    'mostrarsinstock' => filter_input(INPUT_GET, 'mostrarsinstock', FILTER_SANITIZE_NUMBER_INT),
                    'mostrarimagenes' => filter_input(INPUT_GET, 'mostrarimagenes', FILTER_SANITIZE_NUMBER_INT),
                    'idlista' => (filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) : 1 ),
                    'iddeposito' => (filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) : 1 ),
                    'tipo' => (filter_input(INPUT_GET, 'tipo', FILTER_SANITIZE_STRING) ? filter_input(INPUT_GET, 'tipo', FILTER_SANITIZE_STRING) : 'completo'),
                    'mostrarestado' => filter_input(INPUT_GET, 'mostrarestado', FILTER_SANITIZE_NUMBER_INT),
                );
                // Por si no funcionan las opciones del filter_input
                if (!$datos['cantidad']) {
                    $datos['cantidad'] = '10';
                } else if (intval($datos['cantidad']) > 100) {
                    $datos['cantidad'] = '100';
                }
                if (intval($datos['desde']) < 1) {
                    $datos['desde'] = 0;
                }

                $client->listarProductos($datos);
            } else {
                $datos = array(
                    'producto' => $recursos[1],
                    'iue' => $iue,
                    'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),
                    'mostrarcosto' => filter_input(INPUT_GET, 'mostrarcosto', FILTER_SANITIZE_URL),
                    'mostrarestado' => filter_input(INPUT_GET, 'mostrarestado', FILTER_SANITIZE_URL),
                    'idlista' => (filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) : 1 ),
                    'iddeposito' => (filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) : 1 ),
                );
                $client->verProducto($datos);
            }
        } elseif ($metodo == 'POST') {
            if (!$recursos[1]) {
                $datos = file_get_contents('php://input');

                $client->agregarProducto($datos);
            } else {
                switch ($recursos[1]) {
                    case "sincronizar":
                        $datos = file_get_contents('php://input');
                        $client->modificarProductos($datos);
                        break;

                    case "eliminar":
                        if ($recursos[2]) {
                            $iue = filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL);
                            $idProducto = $recursos[2];
                            $client->eliminarProducto($iue, $idProducto);
                        } else {
                            // TODO: Falta rechazar el llamado acá
                        }
                        break;
                }
            }
        } elseif ($metodo == 'PUT') {
            // Acá va el servicio para modificar productos
            $client->rechazarRecurso(400, 'Este recurso no está habilitado');
        } elseif ($metodo == 'DELETE') {
            // Acá va el servicio para eliminar productos
            $client->rechazarRecurso(400, 'Este recurso no está habilitado');
        }
        break;

    case 'combos':
    case 'combosbycodigo':
        if ($metodo == 'GET') {
            if (!$recursos[1]) {

                $datos = array(
                    'iue' => $iue,
                    'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array("options" => array("default" => 0, "min_range" => 0, "max_range" => 1000000))),
                    'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array("options" => array("default" => 100, "min_range" => 1, "max_range" => 1000)))),
                );
                $client->listarCombos($datos);

            } else {
                $datos = array(
                    'iue' => $iue,
                    'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),
                    'idlista' => (filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT) : 1 ),
                    'iddeposito' => (filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) ? filter_input(INPUT_GET, 'iddeposito', FILTER_SANITIZE_NUMBER_INT) : 1 ),             );
                if ($recursos[0] == 'combos') {
                    $datos['idproducto'] = $recursos[1];
                } elseif ($recursos[0] == 'combosbycodigo') {
                    $datos['codigo'] = $recursos[1];
                } else {
                    $client->rechazarRecurso(400, 'Error en id');
                    break;
                }
                $client->verCombo($datos);
            }

        } elseif ($metodo == 'DELETE') {
            $datos['iue'] = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);
            if ($recursos[0] == 'combos') {
                $datos['idproducto'] = $recursos[1];
            } elseif ($recursos[0] == 'combosbycodigo') {
                $datos['codigo'] = $recursos[1];
            }
            $client->eliminarCombo($datos);
        } else {
            if ($metodo == 'POST' || $metodo == 'PUT') {
                $inputJSON = json_decode(file_get_contents('php://input'), true);
                $datos['iue'] = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);
                $datos['body'] = $inputJSON;

                if ($recursos[0] == 'combos') {
                    $datos['idproducto'] = $recursos[1];
                } elseif ($recursos[0] == 'combosbycodigo') {
                    $datos['codigo'] = $recursos[1];
                } else {
                    $client->rechazarRecurso(400, 'Error en id');
                    break;
                }

                $client->editarCombo($datos, $metodo);
            } else {
                $client->rechazarRecurso(400, 'Este recurso no está habilitado');
            }
        }
        break;

    case 'rubros':
        if ($metodo != 'GET') {
            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');
        } else {
            $datos = array(
                'iue' => $iue,
                'idrubro' => filter_input(INPUT_GET, 'idrubro', FILTER_SANITIZE_URL),    //Agrego sub-rubros
            );
            $client->listarRubros($datos);
        }
        break;

    case 'ventas':
        if ($metodo == 'POST') {
            if ($recursos[1]) {
                $idventa = $recursos[1];
                $datos = array(
                    'venta' => $idventa,
                    'external_reference' => filter_input(INPUT_POST, 'external_reference', FILTER_SANITIZE_URL),
                    'iue' => filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL),
                    'datosextras' => filter_input(INPUT_POST, 'datosextras', FILTER_SANITIZE_URL),
                );
                $client->cargarExternalReference($datos);
            } else {

                $datos = array(
                    'iue' => filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL),
                    'idtipoventa' => filter_input(INPUT_POST, 'idtipoventa', FILTER_SANITIZE_URL),
                    'idcliente' => filter_input(INPUT_POST, 'idcliente', FILTER_SANITIZE_URL),
                    'condicionventa' => filter_input(INPUT_POST, 'condicionventa', FILTER_SANITIZE_URL),
                    'fecha' => filter_input(INPUT_POST, 'fecha', FILTER_SANITIZE_URL),
                    'vencimiento1' => filter_input(INPUT_POST, 'vencimiento1', FILTER_SANITIZE_URL),
                    'vencimiento2' => filter_input(INPUT_POST, 'vencimiento2', FILTER_SANITIZE_URL),
                    'descuento' => filter_input(INPUT_POST, 'descuento', FILTER_SANITIZE_URL),
                    'observacion' => filter_input(INPUT_POST, 'observacion', FILTER_SANITIZE_URL),
                    'MP_external_reference' => filter_input(INPUT_POST, 'MP_external_reference', FILTER_SANITIZE_URL),
                    'productos' => array(),
                );

                $i = 1;
                while (isset($_POST[$i . '_idproducto']) || isset($_POST[$i . '_nombre'])) {
                    $datos['productos'][] = array(
                        $i . '_idproducto' => filter_input(INPUT_POST, $i . '_idproducto'),
                        $i . '_codigo' => filter_input(INPUT_POST, $i . '_codigo'),
                        $i . '_cantidad' => filter_input(INPUT_POST, $i . '_cantidad'),
                        $i . '_idunidad' => filter_input(INPUT_POST, $i . '_idunidad'),
                        $i . '_idiva' => filter_input(INPUT_POST, $i . '_idiva'),
                        $i . '_nombre' => filter_input(INPUT_POST, $i . '_nombre'),
                        $i . '_costo' => filter_input(INPUT_POST, $i . '_costo'),
                        $i . '_precio' => filter_input(INPUT_POST, $i . '_precio'),
                        $i . '_descuento' => filter_input(INPUT_POST, $i . '_descuento'),
                        $i . '_observacion' => filter_input(INPUT_POST, $i . '_observacion'),
                    );
                    $i++;
                }
            }
        } else if ($metodo == 'GET') {
            if (!is_numeric($recursos[1])) {
                $datos = array(
                    'iue' => $iue,
                    'busqueda' => filter_input(INPUT_GET, 'busqueda', FILTER_SANITIZE_URL),
                    'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL),
                    'cantidad' => filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL),
                    'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),
                    'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),
                    'idcliente' => filter_input(INPUT_GET, 'idcliente', FILTER_SANITIZE_URL),
                );
                if (!$datos['cantidad']) {
                    $datos['cantidad'] = '10';
                }

                $client->listarVentas($datos);
            } else {
                $idventa = $recursos[1];
                $datos = array(
                    'venta' => $idventa,
                    'iue' => $iue,
                    'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),
                );
                $client->verVenta($datos);
            }
        } else {
            $client->rechazarRecurso(400, 'Este recurso solo acepta GET, PUT y POST como métodos');
        }
        break;

    case 'pedidos':
        if ($metodo == 'GET' && !$recursos[1]) {
            $datos = array(
                'iue' => $iue,
                'busqueda' => '',
                'desde' => '',
                'cantidad' => '',
                'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),
                'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),
                'idrubro' => '',
                'mostrarcosto' => filter_input(INPUT_GET, 'mostrarcosto', FILTER_SANITIZE_URL),
            );
            if (!$datos['cantidad']) {
                $datos['cantidad'] = '10';
            }

            if (!$datos['desde']) {
                $datos['desde'] = '0';
            }

            $client->verPedido($datos);
        } elseif ($metodo == 'POST') {
            // TODO: Validar que tanto el idproducto, como la cantidad sean enteros, además hay que verificar que el idproducto exista
            $idproducto = $recursos[1];
            $cantidad = $recursos[2];
            $client->agregarProductoAlCarrito($idproducto, $cantidad);
        } elseif ($metodo == 'PUT') {
            $idproducto == $recursos[1];
            // TODO: Falta implementar este recurso
            // Actualiza un producto del pedido, por ejemplo la cantidad. Si no existe en el pedido ver si rechazamos la solicitud o lo agregamos
        } elseif ($metodo == 'DELETE') {
            $idproducto = $recursos[1];
            $client->eliminarProductoDelCarrito($idproducto);
        }
        break;

    case 'cerrar':
        if ($metodo != 'POST') {
            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');
        } else {
            $iue = filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL);
            $external_reference = filter_input(INPUT_POST, 'external_reference', FILTER_SANITIZE_URL);
            $datos = array(
                'formapago' => filter_input(INPUT_POST, 'formapago', FILTER_SANITIZE_URL),
                'observacion' => filter_input(INPUT_POST, 'observacion', FILTER_SANITIZE_STRING),
                'envio' => filter_input(INPUT_POST, 'envio', FILTER_SANITIZE_STRING),
                'descripcionenvio' => filter_input(INPUT_POST, 'descripcionenvio', FILTER_SANITIZE_STRING),
                'importeenvio' => filter_input(INPUT_POST, 'importeenvio', FILTER_SANITIZE_STRING),
                'idextra' => filter_input(INPUT_POST, 'idextra', FILTER_SANITIZE_STRING),
                'direccion' => filter_input(INPUT_POST, 'direccion', FILTER_SANITIZE_STRING),
                'ivaenvio' => filter_input(INPUT_POST, 'ivaenvio', FILTER_SANITIZE_STRING)
            );
            if (!$iue)
                $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);
            $client->cerrarPedido($iue, $external_reference, $datos);
        }
        break;

    case 'pedido':
    case 'cerrarpedidoconitems':
        if ($metodo != 'POST') {
            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');
        } else {
            $inputJSON = json_decode(file_get_contents('php://input'), true);
            $iue = $inputJSON['iue'];
            $datos = [];
            $datos['items'] = $inputJSON['items'];
            $datos['descuento'] = $inputJSON['descuento'];
            $datos['total'] = $inputJSON['total'];
            $datos['idlista'] = $inputJSON['idlista'];
            $datos['iddeposito'] = $inputJSON['iddeposito'];
            $datos['idtipoventa'] = $inputJSON['idtipoventa'];
            $datos['estado'] = $inputJSON['estado'];
            $datos['cliente'] = $inputJSON['cliente'];
            $datos['extras'] = $inputJSON['datosextra'];
            $datos['observacion'] = $inputJSON['observacion'];
            $datos['external_reference'] = $inputJSON['external_reference'];

            if ($iue) {
                $client->cerrarPedidoConItems($iue, $datos);
            }
        }
        break;

        case 'anularventa':
            if ($metodo != 'POST') {
                $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');
            } else {
                $inputJSON = json_decode(file_get_contents('php://input'), true);
                $iue = $inputJSON['iue'];
                $idventa = $inputJSON['idventa'];

                if ($iue) {
                    $client->anularventa($iue, $idventa);
                }
            }
            break;

    case 'cerrarconget':
        if ($metodo != 'GET') {
            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');
        } else {
            $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);
            $external_reference = filter_input(INPUT_GET, 'external_reference', FILTER_SANITIZE_URL);
            $client->cerrarPedido($iue, $external_reference, false);
        }
        break;
    case 'cerrarConDatosExtra':
        if ($metodo != 'POST') {
            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');
        } else {
            $iue = filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL);
            $external_reference = filter_input(INPUT_POST, 'external_reference', FILTER_SANITIZE_URL);
            $extras = json_decode(filter_input(INPUT_POST, 'extras', FILTER_SANITIZE_URL));
            $client->cerrarPedidoConDatosExtra($iue, $external_reference, $extras);
        }
        break;

    case 'usuarios':
        if ($metodo == 'GET') {
            if (!$recursos[1]) {
                $client->rechazarRecurso(400, 'Recurso invalido');
            } elseif ($recursos[1] != $_SESSION['usuario']) {
                $client->rechazarRecurso(401, 'No está autorizado a realizar este llamado');
            } else {
                file_put_contents(PATH_LOGS.'api-v1/' . date("Y-m-d") . '.log', "\r\n GET 1 " . $recursos[1], FILE_APPEND);
                $datos = array(
                    'iue' => $iue,
                    'idcliente' => $recursos[1],
                );
                $client->verUsuario($datos);
            }

            // Para crear a un usuario debería venir por PUT, pero no me funcionó, así que lo hago por POST por ahora
        } elseif ($metodo == 'PUT') {
            $client->rechazarRecurso(401, 'No está autorizado a realizar este llamado');
        } elseif ($metodo == 'POST') {
            if (!$recursos[1] && !$_SESSION['usuario']) {
                $datos = array(
                    'iue' => filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL),
                    'idcliente' => $recursos[1],
                    'estado' => 0,
                    'nombre' => filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_STRING),
                    'mail' => filter_input(INPUT_POST, 'mail', FILTER_VALIDATE_EMAIL),
                    'pass' => filter_input(INPUT_POST, 'pass', FILTER_SANITIZE_STRING),
                    'telefonos' => filter_input(INPUT_POST, 'telefonos', FILTER_SANITIZE_STRING),
                    'domicilio' => filter_input(INPUT_POST, 'domicilio', FILTER_SANITIZE_STRING),
                    'idlocalidad' => filter_input(INPUT_POST, 'idlocalidad', FILTER_SANITIZE_NUMBER_INT),
                    'dni' => filter_input(INPUT_POST, 'dni', FILTER_VALIDATE_FLOAT),
                    'idtipoiva' => filter_input(INPUT_POST, 'idtipoiva', FILTER_SANITIZE_NUMBER_INT),
                    'razonSocial' => filter_input(INPUT_POST, 'razonSocial', FILTER_SANITIZE_STRING),
                    'cuit' => filter_input(INPUT_POST, 'cuit', FILTER_VALIDATE_FLOAT),
                );
                if (!$datos['iue'])
                    $datos['iue'] = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);
                $client->crearUsuario($datos);
            } elseif ($recursos[1] && $recursos[1] == $_SESSION['usuario']) {
                $datos = array(
                    'iue' => filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL),
                    'idcliente' => $recursos[1],
                    'nombre' => isset($_POST['nombre'])
                        ? filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_STRING)
                        : false,
                    'mail' => isset($_POST['mail'])
                        ? filter_input(INPUT_POST, 'mail', FILTER_VALIDATE_EMAIL)
                        : false,
                    'pass' => isset($_POST['pass'])
                        ? filter_input(INPUT_POST, 'pass', FILTER_SANITIZE_STRING)
                        : false,
                    'telefonos' => isset($_POST['telefonos'])
                        ? filter_input(INPUT_POST, 'telefonos', FILTER_SANITIZE_STRING)
                        : false,
                    'domicilio' => isset($_POST['domicilio'])
                        ? filter_input(INPUT_POST, 'domicilio', FILTER_SANITIZE_STRING)
                        : false,
                    'idlocalidad' => isset($_POST['idlocalidad'])
                        ? filter_input(INPUT_POST, 'idlocalidad', FILTER_SANITIZE_NUMBER_INT)
                        : false,
                    'dni' => isset($_POST['dni'])
                        ? filter_input(INPUT_POST, 'dni', FILTER_VALIDATE_FLOAT)
                        : false,
                    'idtipoiva' => isset($_POST['idtipoiva'])
                        ? filter_input(INPUT_POST, 'idtipoiva', FILTER_SANITIZE_NUMBER_INT)
                        : false,
                    'razonSocial' => isset($_POST['razonSocial'])
                        ? filter_input(INPUT_POST, 'razonSocial', FILTER_SANITIZE_STRING)
                        : false,
                    'cuit' => isset($_POST['cuit'])
                        ? filter_input(INPUT_POST, 'cuit', FILTER_VALIDATE_FLOAT)
                        : false,
                );
                if (!$datos['iue'])
                    $datos['iue'] = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);
                $client->modificarUsuario($datos);
            } else {
                $client->rechazarRecurso(401, 'No está autorizado a realizar este llamado');
            }
        } else {
            $client->rechazarRecurso(400, 'Recurso invalido');
        }
        break;

    case "pass":
        if ($metodo != 'POST') {
            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');
        } else {
            $iue = filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL);
            $id_usuario = filter_input(INPUT_POST, 'id_usuario', FILTER_SANITIZE_NUMBER_INT);
            $pass_actual = filter_input(INPUT_POST, 'pass_actual', FILTER_SANITIZE_URL);
            $pass_nuevo = filter_input(INPUT_POST, 'pass_nuevo', FILTER_SANITIZE_URL);
            $client->modificarPassUsuario($iue, $id_usuario, $pass_actual, $pass_nuevo);
        }
        break;

    case "login":
        if ($metodo == 'GET') {
            $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);

            $email = filter_input(INPUT_GET, 'email', FILTER_VALIDATE_EMAIL);
            $pass = filter_input(INPUT_GET, 'pass', FILTER_SANITIZE_URL);
            $client->validarUsuario($iue, $email, $pass);
        } else {
            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');
        }
        break;

    case "loginsinpw":
        if ($metodo == 'GET') {
            $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);
            $email = filter_input(INPUT_GET, 'email', FILTER_VALIDATE_EMAIL);
            $client->validarUsuarioSinPw($iue, $email);
        } else {
            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');
        }
        break;

    case "validar":
        if ($metodo == 'GET') {
            $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);
            $random = filter_input(INPUT_GET, 'random', FILTER_SANITIZE_URL);
            $mail = filter_input(INPUT_GET, 'mail', FILTER_VALIDATE_EMAIL);

            switch ($recursos[1]) {
                default:
                    $client->rechazarRecurso(400, 'Recurso inválido');
                    break;

                case 'mail':
                    if ($iue && $random) {
                        $client->confirmarMail($iue, $random);
                    } else {
                        $client->rechazarRecurso(400, 'Recurso inválido');
                    }
                    break;

                case 'pass':
                    if ($iue && $random) {
                        $client->confirmarPass($iue, $random);
                    } else {
                        $client->rechazarRecurso(400, 'Recurso inválido');
                    }
                    break;

                case 'escliente':
                    if ($iue && $mail) {
                        $client->esCliente($iue, $mail, $pass);
                    } else {
                        $client->rechazarRecurso(400, 'Recurso inválido');
                    }
                    break;

                case 'esclienteml':
                    $datos = array(
                        'iue' => $iue,
                        'apodo' => filter_input(INPUT_GET, 'apodo', FILTER_SANITIZE_STRING),
                        'order_id' => filter_input(INPUT_GET, 'order_id', FILTER_SANITIZE_URL)
                    );
                    $client->esClienteMl($datos);
                    break;

                case 'resetearpass':
                    if ($iue && $mail) {
                        $client->resetearPass($iue, $mail);
                    } else {
                        $client->rechazarRecurso(400, 'Recurso inválido');
                    }
                    break;
            }
        } else {
            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');
        }
        break;

    case "forzarmail":
        if ($metodo == 'POST') {
            $datos = array(
                'iue' => filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL),
                'id_cliente' => filter_input(INPUT_POST, 'id_cliente', FILTER_SANITIZE_URL),
                'mail' => filter_input(INPUT_POST, 'mail', FILTER_SANITIZE_URL)
            );
            $client->forzarMail($datos);
        } else {
            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');
        }
        break;

    case "tablas":
        if ($metodo == 'GET') {
            switch ($recursos[1]) {
                default:
                    $client->rechazarRecurso(400, 'Tipo de tabla inválido');
                    break;

                case 'iva':
                    $datos = array(
                        'iue' => $iue,
                    );
                    $client->listarTiposIva($datos);
                    break;
            }
        } else {
            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');
        }
        break;

    case "passForzada":
        if ($metodo != 'POST') {
            $client->rechazarRecurso(400, 'Este recurso solo acepta POST como método');
        } else {
            $iue = filter_input(INPUT_POST, 'iue', FILTER_SANITIZE_URL);
            $id_usuario = filter_input(INPUT_POST, 'id_usuario', FILTER_SANITIZE_NUMBER_INT);
            $pass_nuevo = filter_input(INPUT_POST, 'pass_nuevo', FILTER_SANITIZE_URL);
            $client->modificarPassUsuarioForzada($iue, $id_usuario, $pass_nuevo);
        }
        break;

    case 'cajas':
        if ($metodo == 'GET') {
            if ($recursos[1]) {
                $client->rechazarRecurso(400, 'Recurso invalido');
            } else {
                $datos = array(
                    'iue' => $iue,
                    'idcaja' => filter_input(INPUT_GET, 'idcaja', FILTER_SANITIZE_NUMBER_INT),
                    'idtipocaja' => filter_input(INPUT_GET, 'idtipocaja', FILTER_SANITIZE_NUMBER_INT),
                    'idconcepto' => filter_input(INPUT_GET, 'idconcepto', FILTER_SANITIZE_NUMBER_INT),
                    'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_STRING),
                    'hasta' => filter_input(INPUT_GET, 'hasta', FILTER_SANITIZE_STRING)
                );
                $client->listarMovimientosCajas($datos);
            }
        } else {
            $client->rechazarRecurso(401, 'No está autorizado a realizar este llamado');
        }
        break;

    case 'iva':
        if ($metodo == 'GET') {
            switch ($recursos[1]) {
                default:
                    $client->rechazarRecurso(400, 'Tipo de tabla inválido');
                    break;

                case 'ventas':
                    $datos = array(
                        'iue' => $iue,
                        'periodo' => filter_input(INPUT_GET, 'periodo', FILTER_SANITIZE_URL)
                    );
                    $client->listarLibroIvaVentas($datos);
                    break;

                case 'compras':
                    $datos = array(
                        'iue' => $iue,
                        'periodo' => filter_input(INPUT_GET, 'periodo', FILTER_SANITIZE_URL)
                    );
                    $client->listarLibroIvaCompras($datos);
                    break;
            }
        } else {
            $client->rechazarRecurso(401, 'No está autorizado a realizar este llamado');
        }
        break;

    case 'botonmercadopago':
        require PATH_TOOLS . 'mercadopago/mercadopago.php';
        if ($metodo != 'GET') {
            $client->rechazarRecurso(400, 'Este recurso solo acepta GET como método');
        } else {
            $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);
            $datos = array(
                'titulo' => filter_input(INPUT_GET, 'titulo', FILTER_SANITIZE_URL),
                'cantidad' => filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL),
                'precio' => filter_input(INPUT_GET, 'precio', FILTER_SANITIZE_URL),
                'external_reference' => filter_input(INPUT_GET, 'external_reference', FILTER_SANITIZE_URL),
                'back_urls_success' => filter_input(INPUT_GET, 'back_urls_success', FILTER_SANITIZE_URL),
                'back_urls_failure' => filter_input(INPUT_GET, 'back_urls_failure', FILTER_SANITIZE_URL),
                'back_urls_pending' => filter_input(INPUT_GET, 'back_urls_pending', FILTER_SANITIZE_URL),
                'notification_url' => filter_input(INPUT_GET, 'notification_url', FILTER_SANITIZE_URL),
            );
            $client->crearBotonMP($datos, $iue);
        }
        break;

        case 'clientes':
            if ($metodo == 'GET') {
                if (!$recursos[1]) {
                    $datos = array(
                        'iue' => $iue,
                        'busqueda' => filter_input(INPUT_GET, 'busqueda', FILTER_SANITIZE_STRING),
                        'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array("options" => array("default" => 0, "min_range" => 0, "max_range" => 1000000))),
                        'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array("options" => array("default" => 10, "min_range" => 1, "max_range" => 100)))),
                        'modificados' => filter_input(INPUT_GET, 'modificados', FILTER_VALIDATE_INT, array("options" => array("default" => 0, "min_range" => 1, "max_range" => 1000))),
                        'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),
                        'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),
                        'idtipocliente' => filter_input(INPUT_GET, 'idtipocliente', FILTER_SANITIZE_NUMBER_INT),
                        'idlista' => filter_input(INPUT_GET, 'idlista', FILTER_SANITIZE_NUMBER_INT),
                        'mostrarestado' => filter_input(INPUT_GET, 'mostrarestado', FILTER_SANITIZE_NUMBER_INT),
                        'mostrarmoneda' => filter_input(INPUT_GET, 'mostrarestado', FILTER_SANITIZE_NUMBER_INT),
                    );
                    // Por si no funcionan las opciones del filter_input
                    if (!$datos['cantidad']) {
                        $datos['cantidad'] = '10';
                    } else if (intval($datos['cantidad']) > 100) {
                        $datos['cantidad'] = '100';
                    }
                    if (intval($datos['desde']) < 1) {
                        $datos['desde'] = 0;
                    }

                    $client->listarClientes($datos);
                } else {
                    $datos = array(
                        'idcliente' => $recursos[1],
                        'iue' => $iue,
                        'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),
                    );
                    $client->verCliente($datos);
                }
            } elseif ($metodo == 'POST') {
                if (!$recursos[1]) {
                    $datos = file_get_contents('php://input');
                    $client->agregarCliente($datos);
                } else {
                    $datos = file_get_contents('php://input');
                    $idcliente = $recursos[1];
                    $client->modificarCliente($datos, $idcliente);
                }
            } elseif ($metodo == 'PUT') {
                // Acá va el servicio para modificar productos
                $client->rechazarRecurso(400, 'Este recurso no está habilitado');
            } elseif ($metodo == 'DELETE') {
                if ($recursos[1]) {
                    $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);
                    $idcliente = $recursos[1];
                    $client->eliminarCliente($iue, $idcliente);
                }
            }
            break;

        case 'servicios':
            if ($metodo == 'GET') {
                if (!$recursos[1]) {
                    $datos = array(
                        'iue' => $iue,
                        'busqueda' => filter_input(INPUT_GET, 'busqueda', FILTER_SANITIZE_STRING),
                        'desde' => filter_input(INPUT_GET, 'desde', FILTER_SANITIZE_URL, array("options" => array("default" => 0, "min_range" => 0, "max_range" => 1000000))),
                        'cantidad' => (filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) == 'completo' ? filter_input(INPUT_GET, 'cantidad', FILTER_SANITIZE_URL) :  filter_input(INPUT_GET, 'cantidad', FILTER_VALIDATE_INT, array("options" => array("default" => 10, "min_range" => 1, "max_range" => 100)))),
                        'modificados' => filter_input(INPUT_GET, 'modificados', FILTER_VALIDATE_INT, array("options" => array("default" => 0, "min_range" => 1, "max_range" => 1000))),
                        'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),
                        'orden' => filter_input(INPUT_GET, 'orden', FILTER_SANITIZE_URL),
                        'idtiposervicio' => filter_input(INPUT_GET, 'idtiposervicio', FILTER_SANITIZE_NUMBER_INT),
                        'idcliente' => filter_input(INPUT_GET, 'idcliente', FILTER_SANITIZE_NUMBER_INT),
                        'estado' => filter_input(INPUT_GET, 'estado', FILTER_SANITIZE_NUMBER_INT),

                        'fechasolicitado' => filter_input(INPUT_GET, 'fechasolicitado', FILTER_SANITIZE_STRING),
                        'fechasolicitado_desde' => filter_input(INPUT_GET, 'fechasolicitado_desde', FILTER_SANITIZE_STRING),
                        'fechasolicitado_hasta' => filter_input(INPUT_GET, 'fechasolicitado_hasta', FILTER_SANITIZE_STRING),

                        'fechainicio' => filter_input(INPUT_GET, 'fechainicio', FILTER_SANITIZE_STRING),
                        'fechainicio_desde' => filter_input(INPUT_GET, 'fechainicio_desde', FILTER_SANITIZE_STRING),
                        'fechainicio_hasta' => filter_input(INPUT_GET, 'fechainicio_hasta', FILTER_SANITIZE_STRING),

                        'fechafin' => filter_input(INPUT_GET, 'fechafin', FILTER_SANITIZE_STRING),
                        'fechafin_desde' => filter_input(INPUT_GET, 'fechafin_desde', FILTER_SANITIZE_STRING),
                        'fechafin_hasta' => filter_input(INPUT_GET, 'fechafin_hasta', FILTER_SANITIZE_STRING),

                        'fechalimite' => filter_input(INPUT_GET, 'fechalimite', FILTER_SANITIZE_STRING),
                        'fechalimite_desde' => filter_input(INPUT_GET, 'fechalimite_desde', FILTER_SANITIZE_STRING),
                        'fechalimite_hasta' => filter_input(INPUT_GET, 'fechalimite_hasta', FILTER_SANITIZE_STRING),
                    );
                    // Por si no funcionan las opciones del filter_input
                    if (!$datos['cantidad']) {
                        $datos['cantidad'] = '10';
                    } else if (intval($datos['cantidad']) > 100) {
                        $datos['cantidad'] = '100';
                    }
                    if (intval($datos['desde']) < 1) {
                        $datos['desde'] = 0;
                    }

                    $client->listarServicios($datos);
                } else {
                    $datos = array(
                        'idservicio' => $recursos[1],
                        'iue' => $iue,
                        'datosextras' => filter_input(INPUT_GET, 'datosextras', FILTER_SANITIZE_URL),
                    );
                    $client->verServicio($datos);
                }
            } elseif ($metodo == 'POST') {
                if (!$recursos[1]) {
                    $datos = file_get_contents('php://input');
                    $client->agregarServicio($datos);
                } else {
                    $datos = file_get_contents('php://input');
                    $idservicio = $recursos[1];
                    $client->modificarServicio($datos, $idservicio);
                }
            } elseif ($metodo == 'PUT') {
                // Acá va el servicio para modificar productos
                $client->rechazarRecurso(400, 'Este recurso no está habilitado');
            } elseif ($metodo == 'DELETE') {
                if ($recursos[1]) {
                    $iue = filter_input(INPUT_GET, 'iue', FILTER_SANITIZE_URL);
                    $idservicio = $recursos[1];
                    $client->eliminarServicio($iue, $idservicio);
                }
            }
            break;
}

unlink($consultando_api);