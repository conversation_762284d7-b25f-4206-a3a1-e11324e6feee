# 🤖 ROADS > AGENTE
-------------------------------------------------------------------------------

## 📊 PLAN

## PRÓXIMOS MILESTONES

## SELENIUM EN AGENTE.AR

- [x] Instalar selenium y correr un test
- [x] Crear repositorio git
- [x] Desarrollar un index principal
- [x] Logearse en local
- [x] Prisci con git y deploy
- [x] Deploy con GitLab CD
- [x] Logs y outputs
- [ ] Que se pueda obtener el listado
- [ ] Ejecutar con n8n
- [ ] Lee<PERSON> la devolución
- [ ] Ana<PERSON>zar si se puede guardar sesión
- [ ] Pasar la pelota


Esta idea la estoy trabajando en [AIC.md](./AIC.md)

## TOKENS

En el archivo CloudAPI.postman_environments.json están los datos

**Agente Pri**

EAAVk1XncURIBO9xXcdmWkQcYAYkzY7KXZC6AZA8I6DuV11SNsyrtulJzkmVySdgKgLBYw7fkZBPgg5I1vF2ZA9Io8uTZAwOSMX4agDOAEnSJbxs9kzAoirT9DjBfQ4YVAdIRPfHODnmoFuoFRPZAJbU1b1DgSAPemEf9SKlmj3Pq9t2hZArTa6cVk1dJVpFSVprOAZDZD

**Agente Crono**

EAAHC6N82t8MBO0uAmmIGM6ZAP51IWqocxkTfH4Ed6fXKwBMyViWLUciNjq2Ol4Da7UDmscBivCQjZC4f1gAbKOt7hMRbKbgNmKflbS49XHEDDHNNYZCPTXyMUUOK1tqQAp3ZAZBd36NMZAIqEjEPBRijvpoJhwh6ZCmKCpvIvUzHiB6vHqtdRZCDjG5pFCDfxBy1ZCAZDZD