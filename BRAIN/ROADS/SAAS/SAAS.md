# 📦 ROADS > SAAS
-------------------------------------------------------------------------------

## 📊 PLAN

[PLAN](./PLAN.md)
_En el plan se escribe la visión, misión y objetivos generales pero sin describir las tareas o prioridades_

[KPIs](./KPIs.md)
_En los KPIs se escribe todo lo que se va a medir y como se va a medir_

## PRÓXIMOS MILESTONES

Las próximas prioridades, extraidas de los 3 boards, incluyendo DEV, GROWTH y SOPORTE, son:

**INTEGRACIONES**
OBJETIVO: Tener toda la información de las integraciones, un framework de como agregar una nueva y landigns de todas

**API ARCA**
OBJETIVO: Poder integrar las nuevas obligaciones de ARCA y lograr independizarnos de PyAFIPws por el uso Python 3

**SEPARADOR DE MILES**
OBJETIVO: Recuperar la comodidad de cargar números. Buscar librería de mascara, ver si html alcanza y preguntar a AI


## 🏄‍♂️ BOARDS

[DEV](./DEV.md#milestones-dev)

[GROWTH](./GROWTH.md)

[SOPORTE](./SOPORTE.md)
