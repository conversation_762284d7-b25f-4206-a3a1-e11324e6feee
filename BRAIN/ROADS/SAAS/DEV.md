# 📦 ROADS > SAAS > DEV
-------------------------------------------------------------------------------


## ISSUES PARA DIEGO

- [ ] Crear issue de productos de porcentaje
- [ ] Crear issue para el de recordatorio en productos
- [ ] Cambiar nuestro editor por https://tiptap.dev/
  - Usar los campos que se reemplazan en los mails como {{nombre}} y {{saldo}} en las observaciones al exportar
- [ ] Agregar info al issue de compartir por enlace y el de whatsapp para pasar a Diego
- [ ] Generar issues sobre descomposición de combos
  - Descomponer un combo al agregarlo a una venta, no lo veo difícil pero si hay que pensar si vale la pena y dónde poner esa opción
  - Mostrar las composiciones en las ventas (se me ocurre dentro del desplegable abajo de las observaciones)
  - Lo de mostrar las composiciones en el campo de la descripción en la venta esta bueno. Lo de descomponer el combo también lo pidieron. Ambas pueden ser opciones de comportamiento del producto
  - En el caso de descomponer el producto en la venta, deberíamos tomar los precios directamente de cada producto entiendo, y sus IVAs también. En algunos rubros necesitan eso porque el combo incluye productos con IVAs diferentes y por eso no pueden cargar solo el compuesto, y eso les facilitaría

- [ ] Acceso para contador gratis (nos puede también servir para usuarios de tiendas)
- [ ] Widget buscador de servicios
- [ ] Widget buscador de productos hosteado en SaaS ya está, pero que haya alguna ayuda o algún lugar de widgets
- [ ] Agregar al planning la integración con TiendaNube (avisar a https://mail.google.com/mail/u/0/#inbox/********************************)

- [ ] https://mail.google.com/mail/u/0/#inbox/********************************
- [ ] Evaluar por impuestos en los productos. Me pidieron mostrarlos en la API para las tiendas
  - https://mail.google.com/mail/u/0/#inbox/********************************


-------------------------------------------------------------------------------

## IDEAS PARA EVALUAR METER MINORS AHORA

- Agregar un recordatorio para próximo contacto a los productos
- Ver como funciona lo de {{saldo}} y generar issue para Diego
- Permitir archivos de imágenes en formato webp
- Ver de hacer más diseños con ai
- Unificar js
- Arreglar log con AI. La ip no es la correcta y sacar el equipo
- Revisar slow queries
- Trasabilidad todo a Diego


-------------------------------------------------------------------------------

## INTEGRACIÓN VENTASXMAYOR

- Poder recibir precios diferentes a los que traen los productos ( https://app.saasargentina.com/saas.php?a=verticket&id=10704 )
  Resulta que si tenemos esto en la API, le pasé para que hagan pruebas y lo están viendo
- Ver si podemos especificar el idtipoventa (responder a Fede Plugin SaaS)
  Lo pasamos para la API 2 https://gitlab.com/saasargentina/app/-/issues/2089
- Ver si podemos modificar la venta (responder a Nico Ventasxmayor por Watermelon)
  Lo pasamos para la API 2 https://gitlab.com/saasargentina/app/-/issues/2090
- Enviar newsletters con <NAME_EMAIL>, <EMAIL>


-------------------------------------------------------------------------------

## MILESTONES DEV

**INTEGRACIONES**
OBJETIVO: Tener toda la información de las integraciones, un framework de como agregar una nueva y landigns de todas

- [ ] ISSUE https://gitlab.com/saasargentina/app/-/issues/2152

**API ARCA**
OBJETIVO: Poder integrar las nuevas obligaciones de ARCA y lograr independizarnos de PyAFIPws por el uso Python 3

- [ ] ISSUE https://gitlab.com/saasargentina/app/-/issues/2088

**SEPARADOR DE MILES**
OBJETIVO: Recuperar la comodidad de cargar números. Buscar librería de mascara, ver si html alcanza y preguntar a AI

- [ ] ISSUE https://gitlab.com/saasargentina/app/-/issues/1480

**AYUDA**
OBJETIVO: Tener una herramienta para poder empezar a cargar ayudas en vídeo fácilmente, aprovechando la AI todo lo que se pueda y publicando la información online para SEO

- [ ] ISSUE https://gitlab.com/saasargentina/app/-/issues/1162

**ENLACES Públicos**
OBJETIVO: Poder compartir enlaces públicos de productos, servicios, ventas, compras, etc

- [ ] ISSUE https://gitlab.com/saasargentina/app/-/issues/1901

**API V2**
OBJETIVO: Definir framework para poder contar con esos archivos en API ARCA y empezar a avanzar con MCP e integraciones con una base sólida

- [ ] [API V2](https://gitlab.com/saasargentina/app/-/issues/2037) Tener un framework completo pero sólo Hola Mundo mostrando un producto
- [ ] [API V2 Productos](https://gitlab.com/saasargentina/app/-/issues/2091) Tener todos los recursos de productos

**SITE DESDE MD**

**RAG**
OBJETIVO:

- [ ] Configurar Meta Business**
- [ ] Comprar chip en CABA
- [ ] API ML BETA

**MICRO NICHOS**


---

- Sincronizar ML
  OBJETIVO: Empezar a recuperar el mercado de empresas que se integran con MercadoLibre.
  ISSUES: https://gitlab.com/saasargentina/app/-/issues/1789


- AUTOMATIZACIONES
  OBJETIVO: Poder agregar funcionalidades chicas pero útiles siendo ejecutadas por distintos eventos, todo automatizado y dentro de un módulo de automatizaciones. La tabla ya existe desde 2009 que me está esperando, pero ahora con AI, Zapier, nuestra API y todo lo que están pidiendo los clientes tiene lógica de hacerlo con serverles y con el paradigma "Event-driven programming".
  ISSUES: https://gitlab.com/saasargentina/app/-/issues/1953
  PENSAR:
    - Evaluar toda la implementación con AI
    - Que se pueda ejecutar desde las funciones html (ventana_inicio) como evento y desde API.
    - Para la api que iguale las variables del modulo
    - Toda la documentación en la ayuda para usuarios (no web)

- LOGS
- EMPEZAR DOC.md (Con recuperación ante desastre)
- IMPUESTOS (ARBA, Rosario Y Misiones #1998)
- MÉTRICAS
- PHP8
- WHATSAPP / CHATBOT (Primero ayuda, después trae cosas de la base de datos y en un futuro te permite cargar cosas)
- ABONOS
- VARIACIONES (incluyendo integración con ML)
- TRAZABILIDAD
- INDICADORES
- BOTÓN MÁS INTELIGENTE
- APP EN STORES
- CONTAINERS
- CONTABILIDAD


-------------------------------------------------------------------------------
# SUGERENCIAS DE USUARIO E INTERNAS
-------------------------------------------------------------------------------

### ISSUES PARA GENERAR

- ISSUE API V2 Base: reflotar la API V2 con Laravel u otro framework
- ISSUE API V2 Log Request UUID: Para que se pueda ver el log de las consultas agrupadas
- ISSUE API V2 Composiciones: Para poder listar, modificar, armar, o desarmar combos.
- ISSUE API V2 Listar ventas: Necesito sumar las ventas de una instancia en otra y recuperar los remitos para controlar la carga de camiones con colectores por tipo y nro de comprobante, fecha, cantidad, desde hasta
- ISSUE API V2 Actualización de productos por API con mejor datos y especificando la lista de precios y el depósito
- ISSUE API V2 para seleccionar el tipo de venta en la API: Para que se pueda seleccionar el tipo de venta en la API y que se pueda hacer remitos, facturas y A o B según CUIT
- ISSUE API V2 para cargar pagos: Para que se puedan cargar pagos en la API, hay que ver que configuraciones se agregar para limitar o predeterminar la caja, forma de pago, etc.
- ISSUE API V2 ABM de pagos: Para que se puedan hacer ABM de pagos en la API (Avisar Gezatek https://mail.google.com/mail/u/0/#inbox/********************************)
- ISSUE API V2 para cargar compras a proveedores: Para que se puedan cargar compras a proveedores en la API

- ISSUE API ML BETA: para que se pueda hacer una API de ML en BETA
- ISSUE API ML Cancelar ML: para que se cancele automáticamente la venta en SaaS con otro comprobante
- ISSUE API ML Envío de ML como producto: Listar las distintas formas de envío de ML y poder configurarlas como productos existentes, si no se especifica ningún producto no se carga el envío en el pedido
- ISSUE API ML Pedido de ML como factura: Poder configurar que los pedidos de ML se carguen como facturas electrónicas
- ISSUE API ML Datos de entrega: Agregar a las ventas (ver si también clientes) los datos de entrega de ML. En el caso de Uriel, la provincia a la que se envía es la que utilizan para contabilizar los impuestos de rentas
- ISSUE API ML Depósito de mercadería usada: Para que si una venta se hace con un producto que esté usado, salga de otro depósito que no sea el mismo que los productos nuevos
- ISSUE Partner MP: ahora ofrece toda una configuración para Partners que nos puede servir: https://http2.mlstatic.com/frontend-assets/partners-associations/templates/asociacion_bilateral.pdf


### ISSUES CON USUARIOS ESPERANDO

Previsoft:
- ISSUE para cargar pagos y manegar CC (https://mail.google.com/mail/u/0/#inbox/********************************)

Uriel:
- Informe o forma de descargar las recetas de los combos
- ISSUE para Envio de ML como producto: Cuando tengamos esto, ahí volvemos a ver si te cierra que se auto-cargue los pagos de MP
- ISSUE para Pedido de ML como factura: Lo que charlamos de que sea directamente una factura y se apruebe automáticamente
- ISSUE para Enviar por mail pedidos de ML: Hoy ya lo hace pero tenemos que enviarlo con la factura adjunta post-aprobación de AFIP
- ISSUE para Datos de entrega en todas las ventas: con esto resolveríamos el tema de la jurisdicción para rentas y si metemos algún script para mejorar el proceso de los envíos a Misiones
- ISSUE para Actualización de productos por API con mejor datos y especificando la lista de precios y el depósito
- ISSUE para Depósito de Mercadería en tránsito o reservada: para que el stock de los traslados y de reserva de productos quede ahí
- ISSUE para cancelar ML: para que se cancele automáticamente la venta en SaaS con otro comprobante

Gezatek:
- ISSUE de Sucursales: Limitar los comprobantes que se pueden emitir por sucursal

Gaming:
- ISSUE API ML Depósito de mercadería usada
- Generar facturación automática 7 días después de los pedidos de ML que no tengan la factura ya realizada

Fernando Poggio Opentrace:
- Por otro lado, tenemos automatizado la generación de facturas dado que se utiliza otra forma de facturar
Por ejemplo, tenemos precios diferentes dependiendo de la cantidad del producto
Y también esta el tema de acumular el mismo producto en un solo renglón si se ingresa varias veces al facturar
Con tu sistema tendríamos una factura de muchos renglones dado que cada producto leído con el codigo de barras lo pone en un renglón individual
- ISSUE para listar ventas en la API
- Poder cargar productos en una venta unificando la cantidad en lugar de dejar varias líneas
Eso ya está en el planning, va a salir pronto a ALFA y luego irá a BETA. Tené en cuenta que BETA tiene multimoneda por lo tanto va a tardar en salir
- Aplicar descuentos cuando son varios los productos
Esto lo acabamos de charlar y no entra este año, de ninguna forma. Por lo tanto tenes 2 opciones hoy:
  - Generar combos y en lugar de agregar el producto varias veces, agregar directamente el combo
  - Agregar la venta desde la API con la cantidad del producto y el descuento que le quieras aplicar
Lo vamos a poder resolver con la ampliación del módulo de scripts en cuanto esté, que eso si esperamos arrancarlo este año
- Cargar compras a proveedores
Hoy no tenemos forma de automatizarlo. No podemos hacer cambios en el sistema para que funcione con Selenium. Si podemos agregar esto a la API, lo puedo poner en el planning y avisarte cuando esté.


### IDEAS DE CLIENTES SIN PLANEAR

- Poder re-ordenar los productos dentro de una venta/compra (10624)
- Ordenar fotos de los productos (Lucas Duxton)
- En mi sistema de Proveedores – Cuentas a Pagar .. la pantalla de carga de comprobantes permite leer el código QR de un comprobante, y mediante el des encriptado es posible obtener el contenido del String. Descomponiendo el contenido me permite precargar la pantalla de ingreso de comprobantes con valores como Fechas, Ptos de Venta, Nro de Comprobantes, tomar el CUIT y verificar si existe en base de proveedores o realizar altas automáticas usando Consulta de Padrón A5. Para finalmente poder además constatar el comprobante en AFIP.. recuerden este método.
- BackUp de Listas de precios en S3 exportable para cuando se cae SaaS (Diego NetPatagon)
- De Agustín Suarez: Existe la posibilidad de que me quede siempre tildado este tilde, porque todas las fotos que subo son para enlace publico, asi no nos queda olvidado en la carga de imágenes.


### SUGERENCIAS VARIAS YA PLANEADAS

- Encontré otro sistema que factura automáticamente las compras de mercadolibre y se las adjunta al cliente en la compra. Me ahorra mucho tiempo eso. Con ustedes tenía que generar la factura, descargarla y adjuntarla yo en cada venta de ML
- Múltiples remitos por factura
- 10318 sugiere  "si es posible agregar un link para tomar imagen desde camara directamente de tal manera de poder subirdirectamente laimagen yno tener que hacer el traspaso de imagenes cuando tenemos que subir muchas fotos"


### IDEAS PARA UN AI

- Que detecte que productos se venden juntos y que sugiera combos
- Que detecte que productos no se están vendiendo o no se vendieron nunca y que sugiera bajar el precio o hacer una promoción


### VARIAS IDEAS PARA ISSUES FUTUROS:

- Widgets para SaaS: buscador de productos, servicios pendientes por código, etc (Hacer manual y sitio web)
- Sector para contadores: Listado de informes útiles, herramientas con archivos de AFIP, herramientas para balances y stock con inventario a fecha anterior
- Vendedores 2° etapa: Comisiones de vendedores y Ver los vendedores en la lista de Pedidos pendientes (cuando se activa a ver todos los usuarios), quizás puede ser configurable las columnas
- Sucursales: es agregar una dirección, agrupar usuarios (vendedores), cajas, depósitos y puntos de ventas. Se puede usar por una cuestión de permisos y de informes separados. Uriel pidió limitar los comprobantes según sucursal: https://mail.google.com/mail/u/0/#inbox/********************************
- Informe con fecha de última compra
- Inteligencia en campos usados
- Configuración de notificaciones
- Archivar ventas viejas, especialmente a Uriel

### VER CON ANDIACO

Hola Andiaco, ya casi estamos con multimoneda, así que estoy ordenando varios temas en issues. Tengo un montón sobre API y scrips escritos en archivos pero ahí los voy a ir metiendo cuando arranque con esos temas. Por ahora tengo algunos que no se que hacer, así que te los paso separados por acá para que los veas cuando puedas. Si querés hacemos una call cualquier día que puedas.

HACER ISSUE PARA AHORA
1) Revisar de actualizar los precios de productos al cambiar la cotización de una moneda. Cuando se cambia la cotización de una moneda, no se auto-actualizan los precios en la lista de precios de esa moneda. Sería lógico que no sea automático, pero si que tengamos una opción o herramienta para hacerlo, que supongo que tiene que ir en la herramienta de Ajustar. Ya tenemos la opción de "Por cotización del dólar" que creo que tendríamos que actualizar a "Por cotización de la moneda" y que se pueda seleccionar la moneda a actualizar. Hoy guardamos cuándo fue la última vez que se actualizó por esa cotización, así que tendríamos que agregar esa fecha en todas las monedas y después el cambio no sería tan grande. ¿Te parece que haga un issue así?

HACER 3 ISSUES CON MENOS PRIORIDAD: PRIMERO CABA, DESPUÉS ROSARIO Y POR ÚLTIMO MISIONES
2) ¿Qué prioridad le doy a los impuestos de Misiones, Rosario y CABA? ¿Nos traerá más clientes/facturación? ¿Perdemos usuarios si lo demoramos?
  - Ok, voy a volver a revisar el tema, el está sugiriendo subir el archivo, que es mucho mejor que una API y quizás lo podemos hacer. Cuando lo mire, te escribo la FAQ y te aviso por acá. Si hacemos lo del archivo, actualizo la FAQ. https://mail.google.com/mail/u/0/#inbox/********************************

HACER ISSUE CON BAJA PRIORIDAD
3) Depósito de Mercadería en tránsito o reservada: de distintos pedidos llega la necesidad de tener productos reservados (lo pidió Gezatek o Gaming) y para que el stock de los traslados (lo pidió Uriel).

Se me ocurre que se puede resolver ambos temas con la función de transferencias, agregando un tercer depósito a esa ventana para mercadería en tránsito y que haya doble movimiento si lo seleccionas. Uno al Enviar y otro al Recibir.

Para el tema de la reserva, se me ocurre hacer lo mismo pero en la venta, o sea agregar un segundo depósito para reservar productos. Al cerrar la venta se mueven los productos a ese depósito y queda la venta como pendiente. Al aprobar la venta se restan definitivamente de ese depósito y al anularla se restauran al deposito original.
